import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";
import { parseUnits } from "ethers";

const WageManagementModule = buildModule("WageManagementModule", (m) => {
  // Deploy MockERC20 (USDC) first
  // Parameters for USDC token
  const usdcName = m.getParameter("usdcName", "USD Coin");
  const usdcSymbol = m.getParameter("usdcSymbol", "USDC");
  const usdcInitialSupply = m.getParameter("usdcInitialSupply", parseUnits("1000000", 6)); // 1M USDC with 6 decimals
  
  // Deploy MockERC20 contract
  const usdc = m.contract("MockERC20", [usdcName, usdcSymbol, usdcInitialSupply]);

  // Deploy WageManagement contract with USDC address
  const wageManagement = m.contract("WageManagement", [usdc]);

  return { 
    usdc, 
    wageManagement 
  };
});

export default WageManagementModule;