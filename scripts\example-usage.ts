import { ethers } from "hardhat";

async function main() {
  console.log("🚀 WageManagement Example Usage");
  
  // Get signers
  const [deployer, companyAdmin, laborer1, laborer2, feeWallet] = await ethers.getSigners();
  
  console.log("👥 Accounts:");
  console.log("   Deployer (Super Admin):", deployer.address);
  console.log("   Company Admin:", companyAdmin.address);
  console.log("   Laborer 1:", laborer1.address);
  console.log("   Laborer 2:", laborer2.address);
  console.log("   Fee Wallet:", feeWallet.address);
  
  // Deploy contracts (for example purposes)
  console.log("\n📦 Deploying contracts...");
  
  // Deploy MockERC20 (USDC)
  const MockERC20 = await ethers.getContractFactory("MockERC20");
  const usdc = await MockERC20.deploy("USD Coin", "USDC", ethers.parseUnits("1000000", 6));
  await usdc.waitForDeployment();
  console.log("✅ USDC deployed to:", await usdc.getAddress());
  
  // Deploy WageManagement
  const WageManagement = await ethers.getContractFactory("WageManagement");
  const wageManagement = await WageManagement.deploy(await usdc.getAddress());
  await wageManagement.waitForDeployment();
  console.log("✅ WageManagement deployed to:", await wageManagement.getAddress());
  
  // Example 1: Add a company
  console.log("\n🏢 Example 1: Adding a company");
  const addCompanyTx = await wageManagement.addCompany("Tech Corp", companyAdmin.address);
  await addCompanyTx.wait();
  console.log("✅ Company 'Tech Corp' added with admin:", companyAdmin.address);
  
  const companyId = 1; // First company gets ID 1
  
  // Example 2: Set wage rates
  console.log("\n💰 Example 2: Setting wage rates");
  const wageManagementAsAdmin = wageManagement.connect(companyAdmin) as any;
  
  await (await wageManagementAsAdmin.setWageRate(companyId, "regular", 2000)).wait(); // $20.00/hour
  await (await wageManagementAsAdmin.setWageRate(companyId, "overtime", 3000)).wait(); // $30.00/hour
  console.log("✅ Wage rates set:");
  console.log("   Regular: $20.00/hour");
  console.log("   Overtime: $30.00/hour");
  
  // Example 3: Set fee wallet
  console.log("\n🏦 Example 3: Setting fee wallet");
  await (await wageManagementAsAdmin.setFeeWallet(companyId, feeWallet.address)).wait();
  console.log("✅ Fee wallet set to:", feeWallet.address);
  
  // Example 4: Deposit funds
  console.log("\n💳 Example 4: Depositing funds");
  const depositAmount = ethers.parseUnits("10000", 6); // 10,000 USDC
  
  // Mint USDC to company admin
  const usdcAsDeployer = usdc as any;
  await (await usdcAsDeployer.mint(companyAdmin.address, depositAmount)).wait();
  console.log("✅ Minted 10,000 USDC to company admin");
  
  // Approve and deposit
  const usdcAsAdmin = usdc.connect(companyAdmin) as any;
  await (await usdcAsAdmin.approve(await wageManagement.getAddress(), depositAmount)).wait();
  await (await wageManagementAsAdmin.depositFunds(companyId, depositAmount)).wait();
  console.log("✅ Deposited 10,000 USDC to company balance");
  
  // Example 5: Single wage payment
  console.log("\n💸 Example 5: Single wage payment");
  const hoursWorked = ethers.parseEther("8"); // 8 hours
  
  await (await wageManagementAsAdmin.payWage(
    laborer1.address,
    companyId,
    hoursWorked,
    "regular",
    true // isDailyPayment
  )).wait();
  
  const laborer1Balance = await usdcAsDeployer.balanceOf(laborer1.address);
  console.log("✅ Paid laborer 1 for 8 hours regular work");
  console.log("   Laborer 1 balance:", ethers.formatUnits(laborer1Balance, 6), "USDC");
  
  // Example 6: Batch wage payments
  console.log("\n📦 Example 6: Batch wage payments");
  
  const payments = [
    {
      laborer: laborer1.address,
      companyId: companyId,
      hoursWorked: ethers.parseEther("4"), // 4 hours
      wageType: "overtime",
      isDailyPayment: false // No fees for this payment
    },
    {
      laborer: laborer2.address,
      companyId: companyId,
      hoursWorked: ethers.parseEther("8"), // 8 hours
      wageType: "regular",
      isDailyPayment: true // Daily payment with fees
    }
  ];
  
  // Wait 1 hour to avoid rate limiting (in real scenario)
  // For demo, we'll just proceed
  
  await (await wageManagementAsAdmin.payMultipleWages(payments)).wait();
  
  const laborer1FinalBalance = await usdcAsDeployer.balanceOf(laborer1.address);
  const laborer2Balance = await usdcAsDeployer.balanceOf(laborer2.address);
  const feeWalletBalance = await usdcAsDeployer.balanceOf(feeWallet.address);
  
  console.log("✅ Batch payments completed:");
  console.log("   Laborer 1 final balance:", ethers.formatUnits(laborer1FinalBalance, 6), "USDC");
  console.log("   Laborer 2 balance:", ethers.formatUnits(laborer2Balance, 6), "USDC");
  console.log("   Fee wallet balance:", ethers.formatUnits(feeWalletBalance, 6), "USDC");
  
  // Example 7: Check company info
  console.log("\n📊 Example 7: Company information");
  const companyInfo = await (wageManagement as any).getCompanyInfo(companyId);
  console.log("✅ Company Info:");
  console.log("   Name:", companyInfo.name);
  console.log("   Current Balance:", ethers.formatUnits(companyInfo.currentBalance, 6), "USDC");
  console.log("   Total Deposited:", ethers.formatUnits(companyInfo.totalDeposited, 6), "USDC");
  console.log("   Active:", companyInfo.isActive);
  console.log("   Admins:", companyInfo.admins);
  console.log("   Wage Types:", companyInfo.wageTypes);
  
  console.log("\n🎉 Example usage completed successfully!");
  console.log("\n📝 Summary:");
  console.log("   - Created company with wage rates");
  console.log("   - Deposited funds for payments");
  console.log("   - Processed single and batch payments");
  console.log("   - Collected fees in designated wallet");
  console.log("   - Demonstrated all major contract features");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Example failed:", error);
    process.exit(1);
  });