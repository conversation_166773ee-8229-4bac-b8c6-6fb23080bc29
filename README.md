# WageManagement Smart Contract

A secure smart contract system for managing company wage payments using USDC tokens. This contract provides multi-company wage management with role-based access control, fee management, and batch payment capabilities.

## Features

- **Multi-Company Support**: Manage multiple companies with separate balances and configurations
- **Role-Based Access Control**: Super admins and company-specific admins
- **Flexible Wage Types**: Support for different wage types (hourly, overtime, etc.)
- **Batch Payments**: Process multiple wage payments in a single transaction
- **Fee Management**: Configurable daily payment fees
- **Security**: Built with OpenZeppelin contracts, includes reentrancy protection and pausable functionality
- **Rate Limiting**: Prevents payment spam with minimum time intervals

## Architecture

### Contracts

1. **WageManagement.sol**: Main contract handling all wage management operations
2. **MockERC20.sol**: Test USDC token for development and testing

### Key Components

- **Companies**: Each company has its own balance, admins, and wage rates
- **Wage Types**: Flexible wage categories with different hourly rates
- **Payment Processing**: Single and batch payment capabilities
- **Fee System**: Optional daily payment fees sent to designated wallets

## Installation

```bash
# Install dependencies
npm install

# Compile contracts
npx hardhat compile

# Run tests
npx hardhat test

# Run tests with gas reporting
REPORT_GAS=true npx hardhat test
```

## Deployment

### Development Deployment (Local/Testnet)

For development, the deployment script will automatically deploy a MockERC20 token to simulate USDC:

```bash
# Deploy to local Hardhat network
npx hardhat run scripts/deploy.ts --network localhost

# Deploy to Sepolia testnet
npx hardhat run scripts/deploy.ts --network sepolia

# Using Hardhat Ignition (alternative)
npx hardhat ignition deploy ./ignition/modules/WageManagement.ts --network localhost
```

### Production Deployment

For production networks, the script uses existing USDC contract addresses:

```bash
# Deploy to Ethereum mainnet
npx hardhat run scripts/deploy.ts --network mainnet

# Deploy to Polygon
npx hardhat run scripts/deploy.ts --network polygon

# Deploy to Arbitrum
npx hardhat run scripts/deploy.ts --network arbitrum

# Using Hardhat Ignition with specific USDC address
npx hardhat ignition deploy ./ignition/modules/WageManagementProduction.ts --network mainnet --parameters '{"WageManagementProductionModule": {"usdcAddress": "******************************************"}}'
```

### Supported Networks

The deployment script supports the following networks with pre-configured USDC addresses:

**Mainnets:**
- Ethereum (`ethereum`)
- Polygon (`polygon`)
- Arbitrum (`arbitrum`)
- Optimism (`optimism`)
- Avalanche (`avalanche`)
- Binance Smart Chain (`bsc`)

**Testnets:**
- Sepolia (`sepolia`)
- Goerli (`goerli`)
- Polygon Mumbai (`mumbai`)

**Development:**
- Localhost (`localhost`)
- Hardhat Network (`hardhat`)

## Usage

### 1. Initial Setup (Super Admin)

After deployment, the deployer becomes the first super admin:

```solidity
// Add additional super admins
wageManagement.addSuperAdmin(adminAddress);

// Add a company
wageManagement.addCompany("Company Name", companyAdminAddress);

// Set global daily fee percentage (optional)
wageManagement.changeDailyFeePercentage(500); // 5.00%
```

### 2. Company Configuration (Company Admin)

```solidity
// Set wage rates
wageManagement.setWageRate(companyId, "regular", 1500); // $15.00/hour
wageManagement.setWageRate(companyId, "overtime", 2250); // $22.50/hour

// Set fee wallet (optional)
wageManagement.setFeeWallet(companyId, feeWalletAddress);

// Deposit funds
usdc.approve(wageManagementAddress, amount);
wageManagement.depositFunds(companyId, amount);
```

### 3. Processing Payments

#### Single Payment

```solidity
wageManagement.payWage(
    laborerAddress,
    companyId,
    ethers.parseEther("8"), // 8 hours worked
    "regular",
    true // isDailyPayment (applies fees)
);
```

#### Batch Payments

```solidity
const payments = [
    {
        laborer: laborer1Address,
        companyId: companyId,
        hoursWorked: ethers.parseEther("8"),
        wageType: "regular",
        isDailyPayment: true
    },
    {
        laborer: laborer2Address,
        companyId: companyId,
        hoursWorked: ethers.parseEther("6"),
        wageType: "overtime",
        isDailyPayment: true
    }
];

wageManagement.payMultipleWages(payments);
```

## Contract Addresses

### Mainnet Deployments
- Ethereum: `[To be deployed]`
- Polygon: `[To be deployed]`
- Arbitrum: `[To be deployed]`

### Testnet Deployments
- Sepolia: `[To be deployed]`
- Mumbai: `[To be deployed]`

## Security Features

- **Access Control**: Role-based permissions using OpenZeppelin's AccessControl
- **Reentrancy Protection**: All payment functions protected against reentrancy attacks
- **Pausable**: Emergency pause functionality for super admins
- **Rate Limiting**: Minimum 1-hour interval between payments to same laborer
- **Input Validation**: Comprehensive validation of all inputs
- **Safe Transfers**: Using OpenZeppelin's SafeERC20 for token transfers

## Fee Structure

- **Daily Payment Fee**: Configurable percentage (default 5%, max 20%)
- **Non-Daily Payments**: No fees applied
- **Fee Distribution**: Sent to company's designated fee wallet or held in contract

## Gas Optimization

- **Batch Payments**: Process up to 50 payments in a single transaction
- **Efficient Storage**: Optimized struct packing and storage patterns
- **Minimal External Calls**: Reduced gas costs through batching

## Testing

```bash
# Run all tests
npx hardhat test

# Run specific test file
npx hardhat test test/WageManagementTest.ts

# Run tests with coverage
npx hardhat coverage

# Run tests with gas reporting
REPORT_GAS=true npx hardhat test
```

## Development Commands

```bash
# Compile contracts
npx hardhat compile

# Clean artifacts
npx hardhat clean

# Start local node
npx hardhat node

# Deploy to local network
npx hardhat run scripts/deploy.ts --network localhost

# Verify contract (after deployment)
npx hardhat verify --network mainnet DEPLOYED_CONTRACT_ADDRESS "USDC_ADDRESS"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Audit Status

⚠️ **This contract has not been audited yet. Use at your own risk in production environments.**

## Support

For questions and support, please open an issue in the GitHub repository.
