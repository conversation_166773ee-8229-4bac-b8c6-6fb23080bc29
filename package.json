{"name": "day-pay-smart-contract", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "test:gas": "REPORT_GAS=true hardhat test", "coverage": "hardhat coverage", "node": "hardhat node", "deploy:local": "hardhat run scripts/deploy.ts --network localhost", "deploy:sepolia": "hardhat run scripts/deploy.ts --network sepolia", "deploy:mainnet": "hardhat run scripts/deploy.ts --network mainnet", "deploy:polygon": "hardhat run scripts/deploy.ts --network polygon", "deploy:arbitrum": "hardhat run scripts/deploy.ts --network arbitrum", "ignition:local": "hardhat ignition deploy ./ignition/modules/WageManagement.ts --network localhost", "ignition:sepolia": "hardhat ignition deploy ./ignition/modules/WageManagement.ts --network sepolia", "ignition:mainnet": "hardhat ignition deploy ./ignition/modules/WageManagementProduction.ts --network mainnet", "verify": "hardhat run scripts/verify-deployment.ts", "example": "hardhat run scripts/example-usage.ts --network localhost", "clean": "hardhat clean"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-ignition": "^0.15.0", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "@types/node": ">=18.0.0", "chai": "^4.2.0", "ethers": "^6.4.0", "hardhat": "^2.24.2", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.0", "ts-node": ">=8.0.0", "typechain": "^8.3.0", "typescript": ">=4.5.0"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0", "dotenv": "^16.5.0"}}