// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

/**
 * @title MockERC20
 * @dev Simple ERC20 token used for testing purposes.
 */
contract MockERC20 is ERC20 {
    constructor(string memory name, string memory symbol, uint256 initialSupply) ERC20(name, symbol) {
        // Mint initial supply to the deployer
        _mint(msg.sender, initialSupply);
    }

    /// @notice Mints tokens to a specified address (for testing only)
    /// @param to Recipient of the tokens
    /// @param amount Number of tokens to mint
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}
