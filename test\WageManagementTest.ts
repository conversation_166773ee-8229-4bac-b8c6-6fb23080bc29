const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture } = require("@nomicfoundation/hardhat-network-helpers");

describe("WageManagement Contract", function () {
    // Test fixture for contract deployment
    async function deployWageManagementFixture() {
        const [owner, admin1, admin2, user1, user2, feeWallet] = await ethers.getSigners();
        
        // Deploy mock USDC token
        const MockUSDC = await ethers.getContractFactory("MockERC20");
        const usdc = await MockUSDC.deploy("USDC", "USDC", 6);
        
        // Deploy WageManagement contract
        const WageManagement = await ethers.getContractFactory("WageManagement");
        const wageManagement = await WageManagement.deploy(usdc.target);
        
        // Mint USDC to test accounts
        await usdc.mint(admin1.address, ethers.parseUnits("1000000", 6)); // 1M USDC
        await usdc.mint(admin2.address, ethers.parseUnits("1000000", 6));
        
        return {
            wageManagement,
            usdc,
            owner,
            admin1,
            admin2,
            user1,
            user2,
            feeWallet
        };
    }

    // Helper function to create deactivated company
    async function deployWithDeactivatedCompany() {
        const fixture = await deployWageManagementFixture();
        const { wageManagement, owner, admin1 } = fixture;

        await wageManagement.connect(owner).addCompany("Test Company", admin1.address);
        const companyId = 1;
        // Deactivate the company
        await wageManagement.connect(owner).deactivateCompany(companyId);
        return { ...fixture, companyId };
    }

    // Helper function to create company with setup
    async function deployWithCompanySetup() {
        const fixture = await deployWageManagementFixture();
        const { wageManagement, owner, admin1, usdc } = fixture;

        await wageManagement.connect(owner).addCompany("Test Company", admin1.address);
        const companyId = 1;
        
        // Set up wage rates
        await wageManagement.connect(admin1).setWageRate(companyId, "regular", 1500); // $15.00/hour
        await wageManagement.connect(admin1).setWageRate(companyId, "overtime", 2250); // $22.50/hour
        
        // Deposit funds
        const depositAmount = ethers.parseUnits("10000", 6); // 10,000 USDC
        await usdc.connect(admin1).approve(wageManagement.target, depositAmount);
        await wageManagement.connect(admin1).depositFunds(companyId, depositAmount);
        
        return { ...fixture, companyId };
    }

    // 1. CONSTRUCTOR FUNCTION TESTS
    describe("1. Constructor Function", function () {
        describe("Positive Test Cases", function () {
            it("Should deploy with valid USDC address and set correct initial state", async function () {
                const { wageManagement, usdc, owner } = await loadFixture(deployWageManagementFixture);
                
                // Check USDC address is set correctly
                expect(await wageManagement.usdc()).to.equal(usdc.target);
                
                // Check initial state variables
                expect(await wageManagement.companyCounter()).to.equal(1);
                expect(await wageManagement.dailyFeePercentage()).to.equal(500); // 5%
                
                // Check constants
                expect(await wageManagement.DEFAULT_DAILY_FEE_PERCENTAGE()).to.equal(500);
                expect(await wageManagement.PERCENTAGE_DIVISOR()).to.equal(10000);
                expect(await wageManagement.MAX_FEE_PERCENTAGE()).to.equal(2000);
                expect(await wageManagement.MAX_BATCH_SIZE()).to.equal(50);
                expect(await wageManagement.MAX_HOURS_PER_DAY()).to.equal(ethers.parseEther("24"));
                
                // Check roles are set correctly
                const SUPER_ADMIN_ROLE = await wageManagement.SUPER_ADMIN_ROLE();
                const DEFAULT_ADMIN_ROLE = await wageManagement.DEFAULT_ADMIN_ROLE();
                
                expect(await wageManagement.hasRole(DEFAULT_ADMIN_ROLE, owner.address)).to.be.true;
                expect(await wageManagement.hasRole(SUPER_ADMIN_ROLE, owner.address)).to.be.true;
                expect(await wageManagement.isSuperAdmin(owner.address)).to.be.true;
                
                // Check contract is not paused initially
                expect(await wageManagement.paused()).to.be.false;
            });

            it("Should emit SuperAdminAdded event for deployer", async function () {
                const [owner] = await ethers.getSigners();
                const MockUSDC = await ethers.getContractFactory("MockERC20");
                const usdc = await MockUSDC.deploy("USDC", "USDC", 6);
                
                const WageManagement = await ethers.getContractFactory("WageManagement");
                const wageManagement = await WageManagement.deploy(usdc.target);
                
                await expect(wageManagement.deploymentTransaction())
                    .to.emit(wageManagement, "SuperAdminAdded")
                    .withArgs(owner.address);
            });
        });

        describe("Negative Test Cases", function () {
            it("Should revert when deployed with zero USDC address", async function () {
                const WageManagement = await ethers.getContractFactory("WageManagement");
                
                await expect(
                    WageManagement.deploy(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(WageManagement, "InvalidAddress");
            });
        });

        describe("Error Handling", function () {
            it("Should properly handle InvalidAddress error", async function () {
                const WageManagement = await ethers.getContractFactory("WageManagement");
                
                await expect(
                    WageManagement.deploy(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(WageManagement, "InvalidAddress");
            });
        });

        describe("Event Verification", function () {
            it("Should emit correct SuperAdminAdded event with proper parameters", async function () {
                const [owner] = await ethers.getSigners();
                const MockUSDC = await ethers.getContractFactory("MockERC20");
                const usdc = await MockUSDC.deploy("USDC", "USDC", 6);
                
                const WageManagement = await ethers.getContractFactory("WageManagement");
                const wageManagement = await WageManagement.deploy(usdc.target);
                
                await expect(wageManagement.deploymentTransaction())
                    .to.emit(wageManagement, "SuperAdminAdded")
                    .withArgs(owner.address);
            });
        });
    });

    // 2. ADDSUPERADMIN FUNCTION TESTS
    describe("2. addSuperAdmin Function", function () {
        describe("Positive Test Cases", function () {
            it("Should successfully add a new super admin", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                const SUPER_ADMIN_ROLE = await wageManagement.SUPER_ADMIN_ROLE();
                
                // Verify admin1 is not super admin initially
                expect(await wageManagement.hasRole(SUPER_ADMIN_ROLE, admin1.address)).to.be.false;
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.false;
                
                // Add super admin
                await expect(wageManagement.connect(owner).addSuperAdmin(admin1.address))
                    .to.emit(wageManagement, "SuperAdminAdded")
                    .withArgs(admin1.address);
                
                // Verify admin1 is now super admin
                expect(await wageManagement.hasRole(SUPER_ADMIN_ROLE, admin1.address)).to.be.true;
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.true;
            });

            it("Should allow adding multiple super admins", async function () {
                const { wageManagement, admin1, admin2, owner } = await loadFixture(deployWageManagementFixture);
                
                // Add first super admin
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                
                // Add second super admin using first super admin
                await expect(wageManagement.connect(admin1).addSuperAdmin(admin2.address))
                    .to.emit(wageManagement, "SuperAdminAdded")
                    .withArgs(admin2.address);
                
                // Verify both are super admins
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.true;
                expect(await wageManagement.isSuperAdmin(admin2.address)).to.be.true;
            });

            it("Should allow adding already existing super admin without error", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                // Add super admin first time
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                
                // Add same super admin again - should not revert but also not emit event again
                await expect(wageManagement.connect(owner).addSuperAdmin(admin1.address))
                    .to.not.emit(wageManagement, "SuperAdminAdded");
                
                // Verify still super admin
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.true;
            });
        });

        describe("Negative Test Cases", function () {
            it("Should revert when adding zero address as super admin", async function () {
                const { wageManagement, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).addSuperAdmin(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(wageManagement, "InvalidAddress");
            });

            it("Should revert when non-super admin tries to add super admin", async function () {
                const { wageManagement, admin1, admin2 } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(admin1).addSuperAdmin(admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });
        });

        describe("Error Handling", function () {
            it("Should properly handle InvalidAddress error", async function () {
                const { wageManagement, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).addSuperAdmin(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(wageManagement, "InvalidAddress");
            });

            it("Should properly handle AccessControlUnauthorizedAccount error", async function () {
                const { wageManagement, admin1, admin2 } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(admin1).addSuperAdmin(admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });
        });

        describe("Event Verification", function () {
            it("Should emit SuperAdminAdded event with correct parameters", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(wageManagement.connect(owner).addSuperAdmin(admin1.address))
                    .to.emit(wageManagement, "SuperAdminAdded")
                    .withArgs(admin1.address);
            });

            it("Should not emit event when adding existing super admin", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                
                await expect(wageManagement.connect(owner).addSuperAdmin(admin1.address))
                    .to.not.emit(wageManagement, "SuperAdminAdded");
            });
        });
    });

    // 3. REMOVESUPERADMIN FUNCTION TESTS
    describe("3. removeSuperAdmin Function", function () {
        describe("Positive Test Cases", function () {
            it("Should successfully remove a super admin", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                const SUPER_ADMIN_ROLE = await wageManagement.SUPER_ADMIN_ROLE();
                
                // First add super admin
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                expect(await wageManagement.hasRole(SUPER_ADMIN_ROLE, admin1.address)).to.be.true;
                
                // Remove super admin
                await expect(wageManagement.connect(owner).removeSuperAdmin(admin1.address))
                    .to.emit(wageManagement, "SuperAdminRemoved")
                    .withArgs(admin1.address);
                
                // Verify admin1 is no longer super admin
                expect(await wageManagement.hasRole(SUPER_ADMIN_ROLE, admin1.address)).to.be.false;
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.false;
            });

            it("Should allow removing non-existent super admin without error", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                // Verify admin1 is not super admin
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.false;
                
                // Try to remove non-super admin - should not revert but also not emit event
                await expect(wageManagement.connect(owner).removeSuperAdmin(admin1.address))
                    .to.not.emit(wageManagement, "SuperAdminRemoved");
            });

            it("Should prevent lockout scenario with multiple super admins", async function () {
                const { wageManagement, admin1, admin2, owner } = await loadFixture(deployWageManagementFixture);
                
                // Add two more super admins
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                await wageManagement.connect(owner).addSuperAdmin(admin2.address);
                
                // Admin1 can remove admin2
                await expect(wageManagement.connect(admin1).removeSuperAdmin(admin2.address))
                    .to.emit(wageManagement, "SuperAdminRemoved")
                    .withArgs(admin2.address);
                
                // Verify states
                expect(await wageManagement.isSuperAdmin(admin1.address)).to.be.true;
                expect(await wageManagement.isSuperAdmin(admin2.address)).to.be.false;
                expect(await wageManagement.isSuperAdmin(owner.address)).to.be.true;
            });
        });

        describe("Negative Test Cases", function () {
            it("Should revert when super admin tries to remove themselves", async function () {
                const { wageManagement, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).removeSuperAdmin(owner.address)
                ).to.be.revertedWithCustomError(wageManagement, "CannotRemoveSelf");
                
                // Verify owner is still super admin
                expect(await wageManagement.isSuperAdmin(owner.address)).to.be.true;
            });

            it("Should revert when non-super admin tries to remove super admin", async function () {
                const { wageManagement, admin1, admin2 } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(admin1).removeSuperAdmin(admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });

            it("Should revert when admin tries to remove themselves", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                
                await expect(
                    wageManagement.connect(admin1).removeSuperAdmin(admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "CannotRemoveSelf");
            });
        });

        describe("Error Handling", function () {
            it("Should properly handle CannotRemoveSelf error", async function () {
                const { wageManagement, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).removeSuperAdmin(owner.address)
                ).to.be.revertedWithCustomError(wageManagement, "CannotRemoveSelf");
            });

            it("Should properly handle AccessControlUnauthorizedAccount error", async function () {
                const { wageManagement, admin1, admin2 } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(admin1).removeSuperAdmin(admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });
        });

        describe("Event Verification", function () {
            it("Should emit SuperAdminRemoved event with correct parameters", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await wageManagement.connect(owner).addSuperAdmin(admin1.address);
                
                await expect(wageManagement.connect(owner).removeSuperAdmin(admin1.address))
                    .to.emit(wageManagement, "SuperAdminRemoved")
                    .withArgs(admin1.address);
            });

            it("Should not emit event when removing non-existent super admin", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(wageManagement.connect(owner).removeSuperAdmin(admin1.address))
                    .to.not.emit(wageManagement, "SuperAdminRemoved");
            });
        });
    });

    // 4. ADDCOMPANY FUNCTION TESTS
    describe("4. addCompany Function", function () {
        describe("Positive Test Cases", function () {
            it("Should successfully add a new company with valid parameters", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                const companyName = "Test Company";
                
                // Verify initial state
                expect(await wageManagement.companyCounter()).to.equal(1);
                expect(await wageManagement.adminToCompany(admin1.address)).to.equal(0);
                expect(await wageManagement.isCompanyNameTaken(companyName)).to.be.false;
                
                // Add company
                await expect(wageManagement.connect(owner).addCompany(companyName, admin1.address))
                    .to.emit(wageManagement, "CompanyAdded")
                    .withArgs(1, companyName, admin1.address);
                
                // Verify state changes
                expect(await wageManagement.companyCounter()).to.equal(2);
                expect(await wageManagement.adminToCompany(admin1.address)).to.equal(1);
                expect(await wageManagement.isCompanyNameTaken(companyName)).to.be.true;
                
                // Verify company details
                const companyInfo = await wageManagement.getCompanyInfo(1);
                expect(companyInfo.name).to.equal(companyName);
                expect(companyInfo.admins).to.deep.equal([admin1.address]);
                expect(companyInfo.totalDeposited).to.equal(0);
                expect(companyInfo.currentBalance).to.equal(0);
                expect(companyInfo.feeWallet).to.equal(ethers.ZeroAddress);
                expect(companyInfo.wageTypes).to.deep.equal([]);
                expect(companyInfo.isActive).to.be.true;
                
                // Verify admin status
                expect(await wageManagement.isCompanyAdmin(1, admin1.address)).to.be.true;
            });

            it("Should add multiple companies successfully", async function () {
                const { wageManagement, admin1, admin2, owner } = await loadFixture(deployWageManagementFixture);
                
                // Add first company
                await wageManagement.connect(owner).addCompany("Company 1", admin1.address);
                
                // Add second company
                await wageManagement.connect(owner).addCompany("Company 2", admin2.address);
                
                // Verify both companies exist
                expect(await wageManagement.companyCounter()).to.equal(3);
                expect(await wageManagement.adminToCompany(admin1.address)).to.equal(1);
                expect(await wageManagement.adminToCompany(admin2.address)).to.equal(2);
                
                const company1Info = await wageManagement.getCompanyInfo(1);
                const company2Info = await wageManagement.getCompanyInfo(2);
                
                expect(company1Info.name).to.equal("Company 1");
                expect(company2Info.name).to.equal("Company 2");
                expect(company1Info.isActive).to.be.true;
                expect(company2Info.isActive).to.be.true;
            });

            it("Should set default company fee percentage", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await wageManagement.connect(owner).addCompany("Test Company", admin1.address);
                
                // First set a wage rate to test calculation
                await wageManagement.connect(admin1).setWageRate(1, "regular", 1500); // $15.00/hour
                
                const calculationAfterRate = await wageManagement.calculateWageAmount(
                    1,
                    ethers.parseEther("1"),
                    "regular",
                    true
                );
                
                // Expected: amount = 1500 (15.00), fee = 75 (5% of 1500), total = 1575
                expect(calculationAfterRate.amount).to.equal(1500);
                expect(calculationAfterRate.fee).to.equal(75); // 5% of 1500
                expect(calculationAfterRate.total).to.equal(1575);
            });
        });

        describe("Negative Test Cases", function () {
            it("Should revert when adding company with empty name", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).addCompany("", admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "EmptyCompanyName");
                
                // Verify no state changes
                expect(await wageManagement.companyCounter()).to.equal(1);
                expect(await wageManagement.adminToCompany(admin1.address)).to.equal(0);
            });

            it("Should revert when adding company with name too long (>100 characters)", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                const longName = "A".repeat(101); // 101 characters
                
                await expect(
                    wageManagement.connect(owner).addCompany(longName, admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                
                // Verify no state changes
                expect(await wageManagement.companyCounter()).to.equal(1);
            });

            it("Should revert when adding company with zero address admin", async function () {
                const { wageManagement, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).addCompany("Test Company", ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(wageManagement, "InvalidAddress");
            });

            it("Should revert when admin is already assigned to another company", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                // Add first company
                await wageManagement.connect(owner).addCompany("Company 1", admin1.address);
                
                // Try to add second company with same admin
                await expect(
                    wageManagement.connect(owner).addCompany("Company 2", admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "AdminAlreadyAssigned");
                
                // Verify only one company exists
                expect(await wageManagement.companyCounter()).to.equal(2);
                expect(await wageManagement.adminToCompany(admin1.address)).to.equal(1);
            });

            it("Should revert when company name already exists", async function () {
                const { wageManagement, admin1, admin2, owner } = await loadFixture(deployWageManagementFixture);
                const companyName = "Duplicate Company";
                
                // Add first company
                await wageManagement.connect(owner).addCompany(companyName, admin1.address);
                
                // Try to add second company with same name
                await expect(
                    wageManagement.connect(owner).addCompany(companyName, admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "CompanyNameAlreadyExists");
                
                // Verify only one company with that name exists
                expect(await wageManagement.isCompanyNameTaken(companyName)).to.be.true;
                expect(await wageManagement.companyCounter()).to.equal(2);
            });

            it("Should revert when non-super admin tries to add company", async function () {
                const { wageManagement, admin1, admin2 } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(admin1).addCompany("Test Company", admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });
        });

        describe("Error Handling", function () {
            it("Should properly handle EmptyCompanyName error", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).addCompany("", admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "EmptyCompanyName");
            });

            it("Should properly handle InvalidAmount error for long names", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                const longName = "A".repeat(101);
                
                await expect(
                    wageManagement.connect(owner).addCompany(longName, admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
            });

            it("Should properly handle InvalidAddress error", async function () {
                const { wageManagement, owner } = await loadFixture(deployWageManagementFixture);
                
                await expect(
                    wageManagement.connect(owner).addCompany("Test Company", ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(wageManagement, "InvalidAddress");
            });

            it("Should properly handle AdminAlreadyAssigned error", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                
                await wageManagement.connect(owner).addCompany("Company 1", admin1.address);
                
                await expect(
                    wageManagement.connect(owner).addCompany("Company 2", admin1.address)
                ).to.be.revertedWithCustomError(wageManagement, "AdminAlreadyAssigned");
            });

            it("Should properly handle CompanyNameAlreadyExists error", async function () {
                const { wageManagement, admin1, admin2, owner } = await loadFixture(deployWageManagementFixture);
                const companyName = "Duplicate Company";
                
                await wageManagement.connect(owner).addCompany(companyName, admin1.address);
                
                await expect(
                    wageManagement.connect(owner).addCompany(companyName, admin2.address)
                ).to.be.revertedWithCustomError(wageManagement, "CompanyNameAlreadyExists");
            });
        });

        describe("Event Verification", function () {
            it("Should emit CompanyAdded event with correct parameters", async function () {
                const { wageManagement, admin1, owner } = await loadFixture(deployWageManagementFixture);
                const companyName = "Test Company";
                
                await expect(wageManagement.connect(owner).addCompany(companyName, admin1.address))
                    .to.emit(wageManagement, "CompanyAdded")
                    .withArgs(1, companyName, admin1.address);
            });
        });
    });

    // 5. DEACTIVATECOMPANY FUNCTION TESTS
    describe("5. deactivateCompany Function", function () {
        async function setupCompanyFixture() {
            const fixture = await loadFixture(deployWageManagementFixture);
            const { wageManagement, admin1, owner } = fixture;
            
            // Add a company first
            await wageManagement.connect(owner).addCompany("Test Company", admin1.address);
            
            return { ...fixture, companyId: 1 };
        }

        describe("Positive Test Cases", function () {
            it("Should successfully deactivate an active company", async function () {
                const { wageManagement, owner, companyId } = await setupCompanyFixture();
                
                // Verify company is initially active
                const companyInfoBefore = await wageManagement.getCompanyInfo(companyId);
                expect(companyInfoBefore.isActive).to.be.true;
                
                // Deactivate company
                await expect(wageManagement.connect(owner).deactivateCompany(companyId))
                    .to.emit(wageManagement, "CompanyDeactivated")
                    .withArgs(companyId);
                
                // Verify company is now inactive
                const companyInfoAfter = await wageManagement.getCompanyInfo(companyId);
                expect(companyInfoAfter.isActive).to.be.false;
// Verify other company data remains unchanged
                expect(companyInfoAfter.name).to.equal(companyInfoBefore.name);
                expect(companyInfoAfter.admins).to.deep.equal(companyInfoBefore.admins);
                expect(companyInfoAfter.totalDeposited).to.equal(companyInfoBefore.totalDeposited);
                expect(companyInfoAfter.currentBalance).to.equal(companyInfoBefore.currentBalance);
            });

            it("Should allow deactivating already inactive company without error", async function () {
                const { wageManagement, owner, companyId } = await setupCompanyFixture();
                
                // Deactivate company first time
                await wageManagement.connect(owner).deactivateCompany(companyId);
                
                // Verify company is inactive
                const companyInfo = await wageManagement.getCompanyInfo(companyId);
                expect(companyInfo.isActive).to.be.false;
                
                // Deactivate again - should not revert but also emit event
                await expect(wageManagement.connect(owner).deactivateCompany(companyId))
                    .to.emit(wageManagement, "CompanyDeactivated")
                    .withArgs(companyId);
                
                // Verify still inactive
                const companyInfoAfter = await wageManagement.getCompanyInfo(companyId);
                expect(companyInfoAfter.isActive).to.be.false;
            });

            it("Should handle deactivation with funded company correctly", async function () {
                const { wageManagement, admin1, owner, usdc, companyId } = await setupCompanyFixture();
                
                // Fund the company first
                const depositAmount = ethers.parseUnits("1000", 6); // 1000 USDC
                await usdc.connect(admin1).approve(wageManagement.target, depositAmount);
                await wageManagement.connect(admin1).depositFunds(companyId, depositAmount);
                
                // Verify company has funds
                const companyInfoBefore = await wageManagement.getCompanyInfo(companyId);
                expect(companyInfoBefore.currentBalance).to.equal(depositAmount);
                
                // Deactivate company
                await wageManagement.connect(owner).deactivateCompany(companyId);
                
                // Verify funds remain but company is inactive
                const companyInfoAfter = await wageManagement.getCompanyInfo(companyId);
                expect(companyInfoAfter.isActive).to.be.false;
                expect(companyInfoAfter.currentBalance).to.equal(depositAmount);
                expect(companyInfoAfter.totalDeposited).to.equal(depositAmount);
            });
        });

        describe("Negative Test Cases", function () {
            it("Should revert when trying to deactivate non-existent company", async function () {
                const { wageManagement, owner } = await setupCompanyFixture();
                const nonExistentCompanyId = 999;
                
                await expect(
                    wageManagement.connect(owner).deactivateCompany(nonExistentCompanyId)
                ).to.be.revertedWithCustomError(wageManagement, "CompanyNotFound");
            });

            it("Should revert when non-super admin tries to deactivate company", async function () {
                const { wageManagement, admin1, companyId } = await setupCompanyFixture();
                
                await expect(
                    wageManagement.connect(admin1).deactivateCompany(companyId)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });

            it("Should prevent company admin operations after deactivation", async function () {
                const { wageManagement, admin1, owner, companyId } = await setupCompanyFixture();
                
                // Deactivate company
                await wageManagement.connect(owner).deactivateCompany(companyId);
                
                // Try to perform admin operations - should fail
                await expect(
                    wageManagement.connect(admin1).setWageRate(companyId, "regular", 1500)
                ).to.be.revertedWithCustomError(wageManagement, "CompanyInactive");
                
                await expect(
                    wageManagement.connect(admin1).depositFunds(companyId, 1000)
                ).to.be.revertedWithCustomError(wageManagement, "CompanyInactive");
            });
        });

        describe("Error Handling", function () {
            it("Should properly handle CompanyNotFound error", async function () {
                const { wageManagement, owner } = await setupCompanyFixture();
                
                await expect(
                    wageManagement.connect(owner).deactivateCompany(999)
                ).to.be.revertedWithCustomError(wageManagement, "CompanyNotFound");
            });

            it("Should properly handle AccessControlUnauthorizedAccount error", async function () {
                const { wageManagement, admin1, companyId } = await setupCompanyFixture();
                
                await expect(
                    wageManagement.connect(admin1).deactivateCompany(companyId)
                ).to.be.revertedWithCustomError(wageManagement, "AccessControlUnauthorizedAccount");
            });
        });

        describe("Event Verification", function () {
            it("Should emit CompanyDeactivated event with correct parameters", async function () {
                const { wageManagement, owner, companyId } = await setupCompanyFixture();
                
                await expect(wageManagement.connect(owner).deactivateCompany(companyId))
                    .to.emit(wageManagement, "CompanyDeactivated")
                    .withArgs(companyId);
            });
        });
    });

    // Additional test functions for remaining contract functions
    describe("Additional Function Tests", function () {
        describe("setWageRate Function", function () {
            async function setupCompanyForWageRates() {
                const fixture = await loadFixture(deployWageManagementFixture);
                const { wageManagement, owner, admin1 } = fixture;
                
                await wageManagement.connect(owner).addCompany("Test Company", admin1.address);
                const companyId = 1;
                
                return { ...fixture, companyId };
            }

            describe("Positive Test Cases", function () {
                it("Should successfully set wage rate for new wage type", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    const wageType = "regular";
                    const hourlyRate = 1500; // $15.00/hour
                    
                    const tx = await wageManagement.connect(admin1).setWageRate(companyId, wageType, hourlyRate);
                    
                    // Check event emission
                    await expect(tx)
                        .to.emit(wageManagement, "WageRateSet")
                        .withArgs(companyId, wageType, hourlyRate);
                    
                    // Verify wage rate is set
                    expect(await wageManagement.getWageRate(companyId, wageType)).to.equal(hourlyRate);
                    
                    // Verify wage type is added to array
                    const companyInfo = await wageManagement.getCompanyInfo(companyId);
                    expect(companyInfo.wageTypes).to.include(wageType);
                });

                it("Should successfully update existing wage rate", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    const wageType = "regular";
                    const initialRate = 1500;
                    const updatedRate = 1800;
                    
                    // Set initial rate
                    await wageManagement.connect(admin1).setWageRate(companyId, wageType, initialRate);
                    expect(await wageManagement.getWageRate(companyId, wageType)).to.equal(initialRate);
                    
                    // Update rate
                    const tx = await wageManagement.connect(admin1).setWageRate(companyId, wageType, updatedRate);
                    
                    await expect(tx)
                        .to.emit(wageManagement, "WageRateSet")
                        .withArgs(companyId, wageType, updatedRate);
                    
                    expect(await wageManagement.getWageRate(companyId, wageType)).to.equal(updatedRate);
                });

                it("Should allow setting multiple wage types", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    const wageTypes = ["regular", "overtime", "holiday"];
                    const rates = [1500, 2250, 3000];
                    
                    for (let i = 0; i < wageTypes.length; i++) {
                        await wageManagement.connect(admin1).setWageRate(companyId, wageTypes[i], rates[i]);
                        expect(await wageManagement.getWageRate(companyId, wageTypes[i])).to.equal(rates[i]);
                    }
                    
                    const companyInfo = await wageManagement.getCompanyInfo(companyId);
                    expect(companyInfo.wageTypes.length).to.equal(3);
                });
            });

            describe("Negative Test Cases", function () {
                it("Should revert when wage type is empty", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    await expect(
                        wageManagement.connect(admin1).setWageRate(companyId, "", 1500)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidWageType");
                });

                it("Should revert when wage type is too long", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    const longWageType = "A".repeat(51); // 51 characters
                    
                    await expect(
                        wageManagement.connect(admin1).setWageRate(companyId, longWageType, 1500)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidWageType");
                });

                it("Should revert when hourly rate is zero", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    await expect(
                        wageManagement.connect(admin1).setWageRate(companyId, "regular", 0)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });

                it("Should revert when hourly rate exceeds maximum", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    const excessiveRate = (1000000 * 100) + 1; // $10,000.01/hour
                    
                    await expect(
                        wageManagement.connect(admin1).setWageRate(companyId, "regular", excessiveRate)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });
            });

            describe("Error Handling", function () {
                it("Should properly handle InvalidWageType error", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    await expect(
                        wageManagement.connect(admin1).setWageRate(companyId, "", 1500)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidWageType");
                });

                it("Should properly handle InvalidAmount error", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    await expect(
                        wageManagement.connect(admin1).setWageRate(companyId, "regular", 0)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });
            });

            describe("Event Verification", function () {
                it("Should emit WageRateSet event with correct parameters", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForWageRates();
                    
                    const wageType = "regular";
                    const hourlyRate = 1500;
                    
                    await expect(wageManagement.connect(admin1).setWageRate(companyId, wageType, hourlyRate))
                        .to.emit(wageManagement, "WageRateSet")
                        .withArgs(companyId, wageType, hourlyRate);
                });
            });
        });

        describe("depositFunds Function", function () {
            async function setupCompanyForDeposit() {
                const fixture = await loadFixture(deployWageManagementFixture);
                const { wageManagement, owner, admin1, usdc } = fixture;
                
                await wageManagement.connect(owner).addCompany("Test Company", admin1.address);
                const companyId = 1;
                
                return { ...fixture, companyId };
            }

            describe("Positive Test Cases", function () {
                it("Should successfully deposit funds", async function () {
                    const { wageManagement, admin1, usdc, companyId } = await setupCompanyForDeposit();
                    
                    const depositAmount = ethers.parseUnits("1000", 6); // 1000 USDC
                    
                    // Approve transfer
                    await usdc.connect(admin1).approve(wageManagement.target, depositAmount);
                    
                    const tx = await wageManagement.connect(admin1).depositFunds(companyId, depositAmount);
                    
                    // Check event emission
                    await expect(tx)
                        .to.emit(wageManagement, "FundsDeposited")
                        .withArgs(companyId, depositAmount, depositAmount);
                    
                    // Verify company balance
                    const companyInfo = await wageManagement.getCompanyInfo(companyId);
                    expect(companyInfo.currentBalance).to.equal(depositAmount);
                    expect(companyInfo.totalDeposited).to.equal(depositAmount);
                    
                    // Verify USDC transfer
                    expect(await usdc.balanceOf(wageManagement.target)).to.equal(depositAmount);
                });

                it("Should handle multiple deposits correctly", async function () {
                    const { wageManagement, admin1, usdc, companyId } = await setupCompanyForDeposit();
                    
                    const firstDeposit = ethers.parseUnits("500", 6);
                    const secondDeposit = ethers.parseUnits("300", 6);
                    const totalDeposit = firstDeposit + secondDeposit;
                    
                    // First deposit
                    await usdc.connect(admin1).approve(wageManagement.target, firstDeposit);
                    await wageManagement.connect(admin1).depositFunds(companyId, firstDeposit);
                    
                    // Second deposit
                    await usdc.connect(admin1).approve(wageManagement.target, secondDeposit);
                    const tx = await wageManagement.connect(admin1).depositFunds(companyId, secondDeposit);
                    
                    await expect(tx)
                        .to.emit(wageManagement, "FundsDeposited")
                        .withArgs(companyId, secondDeposit, totalDeposit);
                    
                    const companyInfo = await wageManagement.getCompanyInfo(companyId);
                    expect(companyInfo.currentBalance).to.equal(totalDeposit);
                    expect(companyInfo.totalDeposited).to.equal(totalDeposit);
                });
            });

            describe("Negative Test Cases", function () {
                it("Should revert when depositing zero amount", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForDeposit();
                    
                    await expect(
                        wageManagement.connect(admin1).depositFunds(companyId, 0)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });

                it("Should revert when contract is paused", async function () {
                    const { wageManagement, owner, admin1, usdc, companyId } = await setupCompanyForDeposit();
                    
                    // Pause contract
                    await wageManagement.connect(owner).pause();
                    
                    const depositAmount = ethers.parseUnits("1000", 6);
                    await usdc.connect(admin1).approve(wageManagement.target, depositAmount);
                    
                    await expect(
                        wageManagement.connect(admin1).depositFunds(companyId, depositAmount)
                    ).to.be.revertedWithCustomError(wageManagement, "EnforcedPause");
                });
            });

            describe("Error Handling", function () {
                it("Should properly handle InvalidAmount error", async function () {
                    const { wageManagement, admin1, companyId } = await setupCompanyForDeposit();
                    
                    await expect(
                        wageManagement.connect(admin1).depositFunds(companyId, 0)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });

                it("Should properly handle EnforcedPause error", async function () {
                    const { wageManagement, owner, admin1, usdc, companyId } = await setupCompanyForDeposit();
                    
                    await wageManagement.connect(owner).pause();
                    
                    const depositAmount = ethers.parseUnits("1000", 6);
                    await usdc.connect(admin1).approve(wageManagement.target, depositAmount);
                    
                    await expect(
                        wageManagement.connect(admin1).depositFunds(companyId, depositAmount)
                    ).to.be.revertedWithCustomError(wageManagement, "EnforcedPause");
                });
            });

            describe("Event Verification", function () {
                it("Should emit FundsDeposited event with correct parameters", async function () {
                    const { wageManagement, admin1, usdc, companyId } = await setupCompanyForDeposit();
                    
                    const depositAmount = ethers.parseUnits("1000", 6);
                    await usdc.connect(admin1).approve(wageManagement.target, depositAmount);
                    
                    await expect(wageManagement.connect(admin1).depositFunds(companyId, depositAmount))
                        .to.emit(wageManagement, "FundsDeposited")
                        .withArgs(companyId, depositAmount, depositAmount);
                });
            });
        });

        describe("payWage Function", function () {
            describe("Positive Test Cases", function () {
                it("Should successfully pay wage to laborer", async function () {
                    const { wageManagement, admin1, user1, usdc, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const hoursWorked = ethers.parseEther("8"); // 8 hours
                    const wageType = "regular";
                    const isDailyPayment = false;
                    
                    // Calculate expected payment
                    const calculation = await wageManagement.calculateWageAmount(companyId, hoursWorked, wageType, isDailyPayment);
                    const expectedAmount = calculation.amount;
                    const expectedFee = calculation.fee;
                    
                    const laborerBalanceBefore = await usdc.balanceOf(user1.address);
                    const companyBalanceBefore = (await wageManagement.getCompanyInfo(companyId)).currentBalance;
                    
                    const tx = await wageManagement.connect(admin1).payWage(user1.address, companyId, hoursWorked, wageType, isDailyPayment);
                    
                    // Check event emission
                    await expect(tx)
                        .to.emit(wageManagement, "WagePaid")
                        .withArgs(companyId, user1.address, expectedAmount, hoursWorked, wageType, isDailyPayment, expectedFee);
                    
                    // Verify balances
                    const laborerBalanceAfter = await usdc.balanceOf(user1.address);
                    const companyBalanceAfter = (await wageManagement.getCompanyInfo(companyId)).currentBalance;
                    
                    expect(laborerBalanceAfter - laborerBalanceBefore).to.equal(expectedAmount);
                    expect(companyBalanceBefore - companyBalanceAfter).to.equal(expectedAmount + expectedFee);
                    
                    // Verify last payment time is updated
                    expect(await wageManagement.getLastPaymentTime(user1.address, companyId)).to.be.greaterThan(0);
                });

                it("Should pay wage with daily fee when isDailyPayment is true", async function () {
                    const { wageManagement, admin1, user1, feeWallet, usdc, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    // Set fee wallet
                    await wageManagement.connect(admin1).setFeeWallet(companyId, feeWallet.address);
                    
                    const hoursWorked = ethers.parseEther("8");
                    const wageType = "regular";
                    const isDailyPayment = true;
                    
                    const calculation = await wageManagement.calculateWageAmount(companyId, hoursWorked, wageType, isDailyPayment);
                    const expectedFee = calculation.fee;
                    
                    expect(expectedFee).to.be.greaterThan(0); // Should have fee for daily payment
                    
                    const feeWalletBalanceBefore = await usdc.balanceOf(feeWallet.address);
                    
                    await wageManagement.connect(admin1).payWage(user1.address, companyId, hoursWorked, wageType, isDailyPayment);
                    
                    const feeWalletBalanceAfter = await usdc.balanceOf(feeWallet.address);
                    expect(feeWalletBalanceAfter - feeWalletBalanceBefore).to.equal(expectedFee);
                });
            });

            describe("Negative Test Cases", function () {
                it("Should revert when laborer address is zero", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(ethers.ZeroAddress, companyId, ethers.parseEther("8"), "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAddress");
                });

                it("Should revert when hours worked is zero", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, 0, "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidHours");
                });

                it("Should revert when hours worked exceeds maximum", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const excessiveHours = ethers.parseEther("25"); // More than 24 hours
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, excessiveHours, "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "ExcessiveHours");
                });

                it("Should revert when wage type not found", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, ethers.parseEther("8"), "nonexistent", false)
                    ).to.be.revertedWithCustomError(wageManagement, "WageTypeNotFound");
                });

                it("Should revert when payment too frequent (rate limiting)", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    // Make first payment
                    await wageManagement.connect(admin1).payWage(user1.address, companyId, ethers.parseEther("4"), "regular", false);
                    
                    // Try to make second payment immediately
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, ethers.parseEther("4"), "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "PaymentTooFrequent");
                });
            });

            describe("Error Handling", function () {
                it("Should properly handle InvalidAddress error", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(ethers.ZeroAddress, companyId, ethers.parseEther("8"), "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAddress");
                });

                it("Should properly handle InvalidHours error", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, 0, "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidHours");
                });

                it("Should properly handle ExcessiveHours error", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const excessiveHours = ethers.parseEther("25");
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, excessiveHours, "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "ExcessiveHours");
                });

                it("Should properly handle WageTypeNotFound error", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, ethers.parseEther("8"), "nonexistent", false)
                    ).to.be.revertedWithCustomError(wageManagement, "WageTypeNotFound");
                });

                it("Should properly handle PaymentTooFrequent error", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await wageManagement.connect(admin1).payWage(user1.address, companyId, ethers.parseEther("4"), "regular", false);
                    
                    await expect(
                        wageManagement.connect(admin1).payWage(user1.address, companyId, ethers.parseEther("4"), "regular", false)
                    ).to.be.revertedWithCustomError(wageManagement, "PaymentTooFrequent");
                });
            });

            describe("Event Verification", function () {
                it("Should emit WagePaid event with correct parameters", async function () {
                    const { wageManagement, admin1, user1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const hoursWorked = ethers.parseEther("8");
                    const wageType = "regular";
                    const isDailyPayment = false;
                    
                    const calculation = await wageManagement.calculateWageAmount(companyId, hoursWorked, wageType, isDailyPayment);
                    const expectedAmount = calculation.amount;
                    const expectedFee = calculation.fee;
                    
                    await expect(wageManagement.connect(admin1).payWage(user1.address, companyId, hoursWorked, wageType, isDailyPayment))
                        .to.emit(wageManagement, "WagePaid")
                        .withArgs(companyId, user1.address, expectedAmount, hoursWorked, wageType, isDailyPayment, expectedFee);
                });
            });
        });

        describe("emergencyWithdraw Function", function () {
            describe("Positive Test Cases", function () {
                it("Should successfully withdraw funds in emergency", async function () {
                    const { wageManagement, admin1, usdc, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const withdrawAmount = ethers.parseUnits("5000", 6); // $5000
                    const adminBalanceBefore = await usdc.balanceOf(admin1.address);
                    const companyBalanceBefore = (await wageManagement.getCompanyInfo(companyId)).currentBalance;
                    
                    const tx = await wageManagement.connect(admin1).emergencyWithdraw(companyId, withdrawAmount);
                    
                    // Check event emission
                    await expect(tx)
                        .to.emit(wageManagement, "EmergencyWithdraw")
                        .withArgs(companyId, admin1.address, withdrawAmount);
                    
                    // Verify balances
                    const adminBalanceAfter = await usdc.balanceOf(admin1.address);
                    const companyBalanceAfter = (await wageManagement.getCompanyInfo(companyId)).currentBalance;
                    
                    expect(adminBalanceAfter - adminBalanceBefore).to.equal(withdrawAmount);
                    expect(companyBalanceBefore - companyBalanceAfter).to.equal(withdrawAmount);
                });
            });

            describe("Negative Test Cases", function () {
                it("Should revert when withdraw amount is zero", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).emergencyWithdraw(companyId, 0)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });

                it("Should revert when withdraw amount exceeds balance", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const companyBalance = (await wageManagement.getCompanyInfo(companyId)).currentBalance;
                    const excessiveAmount = companyBalance + ethers.parseUnits("1", 6);
                    
                    await expect(
                        wageManagement.connect(admin1).emergencyWithdraw(companyId, excessiveAmount)
                    ).to.be.revertedWithCustomError(wageManagement, "InsufficientBalance");
                });
            });

            describe("Error Handling", function () {
                it("Should properly handle InvalidAmount error", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    await expect(
                        wageManagement.connect(admin1).emergencyWithdraw(companyId, 0)
                    ).to.be.revertedWithCustomError(wageManagement, "InvalidAmount");
                });

                it("Should properly handle InsufficientBalance error", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const companyBalance = (await wageManagement.getCompanyInfo(companyId)).currentBalance;
                    const excessiveAmount = companyBalance + ethers.parseUnits("1", 6);
                    
                    await expect(
                        wageManagement.connect(admin1).emergencyWithdraw(companyId, excessiveAmount)
                    ).to.be.revertedWithCustomError(wageManagement, "InsufficientBalance");
                });
            });

            describe("Event Verification", function () {
                it("Should emit EmergencyWithdraw event with correct parameters", async function () {
                    const { wageManagement, admin1, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const withdrawAmount = ethers.parseUnits("5000", 6);
                    
                    await expect(wageManagement.connect(admin1).emergencyWithdraw(companyId, withdrawAmount))
                        .to.emit(wageManagement, "EmergencyWithdraw")
                        .withArgs(companyId, admin1.address, withdrawAmount);
                });
            });
        });

        describe("View Functions", function () {
            describe("calculateWageAmount", function () {
                it("Should calculate wage amount correctly without daily fee", async function () {
                    const { wageManagement, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const hoursWorked = ethers.parseEther("8");
                    const wageType = "regular";
                    const isDailyPayment = false;
                    
                    const result = await wageManagement.calculateWageAmount(companyId, hoursWorked, wageType, isDailyPayment);
                    
                    // 8 hours * $15/hour = $120 = 12000 cents
                    expect(result.amount).to.equal(12000);
                    expect(result.fee).to.equal(0); // No fee for non-daily payment
                    expect(result.total).to.equal(12000);
                });

                it("Should calculate wage amount correctly with daily fee", async function () {
                    const { wageManagement, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const hoursWorked = ethers.parseEther("8");
                    const wageType = "regular";
                    const isDailyPayment = true;
                    
                    const result = await wageManagement.calculateWageAmount(companyId, hoursWorked, wageType, isDailyPayment);
                    
                    // 8 hours * $15/hour = $120 = 12000 cents
                    // Fee: 5% of 12000 = 600 cents
                    expect(result.amount).to.equal(12000);
                    expect(result.fee).to.equal(600);
                    expect(result.total).to.equal(12600);
                });
            });

            describe("getCompanyWageRates", function () {
                it("Should return all wage rates for company", async function () {
                    const { wageManagement, companyId } = await loadFixture(deployWithCompanySetup);
                    
                    const result = await wageManagement.getCompanyWageRates(companyId);
                    
                    expect(result.wageTypes.length).to.equal(2);
                    expect(result.rates.length).to.equal(2);
                    expect(result.wageTypes).to.include("regular");
                    expect(result.wageTypes).to.include("overtime");
                    expect(result.rates[0]).to.equal(1500); // regular rate
                    expect(result.rates[1]).to.equal(2250); // overtime rate
                });
            });

            describe("getCurrentCompanyId", function () {
                it("Should return correct current company ID", async function () {
                    const { wageManagement, owner, admin1, admin2 } = await loadFixture(deployWageManagementFixture);
                    
                    // Initially should be 0 (no companies created)
                    expect(await wageManagement.getCurrentCompanyId()).to.equal(0);
                    
                    // Add first company
                    await wageManagement.connect(owner).addCompany("Company 1", admin1.address);
                    expect(await wageManagement.getCurrentCompanyId()).to.equal(1);
                    
                    // Add second company
                    await wageManagement.connect(owner).addCompany("Company 2", admin2.address);
                    expect(await wageManagement.getCurrentCompanyId()).to.equal(2);
                });
            });
        });
    });
});