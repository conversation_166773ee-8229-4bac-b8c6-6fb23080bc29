// Network configuration for USDC addresses
export const NETWORK_CONFIG = {
  // Mainnets
  ethereum: {
    usdc: "******************************************",
    name: "Ethereum Mainnet"
  },
  polygon: {
    usdc: "******************************************",
    name: "Polygon Mainnet"
  },
  arbitrum: {
    usdc: "******************************************",
    name: "Arbitrum One"
  },
  optimism: {
    usdc: "******************************************",
    name: "Optimism Mainnet"
  },
  avalanche: {
    usdc: "******************************************",
    name: "Avalanche C-Chain"
  },
  bsc: {
    usdc: "******************************************",
    name: "Binance Smart Chain"
  },
  
  // Testnets
  sepolia: {
    usdc: "******************************************", // USDC on Sepolia
    name: "Sepolia Testnet"
  },
  goerli: {
    usdc: "******************************************", // USDC on Goerli
    name: "Goerli Testnet"
  },
  mumbai: {
    usdc: "******************************************", // USDC on Mumbai
    name: "Polygon Mumbai"
  },
  
  // Local/Development
  localhost: {
    usdc: null, // Will deploy MockERC20
    name: "Local Development"
  },
  hardhat: {
    usdc: null, // Will deploy MockERC20
    name: "Hardhat Network"
  }
};

export function getNetworkConfig(networkName: string) {
  const config = NETWORK_CONFIG[networkName as keyof typeof NETWORK_CONFIG];
  if (!config) {
    throw new Error(`Network ${networkName} not supported. Available networks: ${Object.keys(NETWORK_CONFIG).join(', ')}`);
  }
  return config;
}