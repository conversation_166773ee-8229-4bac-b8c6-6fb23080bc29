import { ethers } from "hardhat";

async function main() {
  // Get deployment addresses from command line arguments or environment variables
  const wageManagementAddress = process.env.WAGE_MANAGEMENT_ADDRESS || process.argv[2];
  const usdcAddress = process.env.USDC_ADDRESS || process.argv[3];

  if (!wageManagementAddress) {
    console.error("❌ Please provide WageManagement contract address");
    console.log("Usage: npx hardhat run scripts/verify-deployment.ts --network <network> <wageManagementAddress> [usdcAddress]");
    process.exit(1);
  }

  console.log("🔍 Verifying WageManagement deployment...");
  console.log("📍 WageManagement Address:", wageManagementAddress);
  
  try {
    // Get contract instance
    const WageManagement = await ethers.getContractFactory("WageManagement");
    const wageManagement = WageManagement.attach(wageManagementAddress) as any;
    
    // Verify basic contract functionality
    console.log("\n📋 Contract Information:");
    
    // Check USDC address
    const contractUsdcAddress = await wageManagement.usdc();
    console.log("💰 USDC Address:", contractUsdcAddress);
    
    if (usdcAddress && contractUsdcAddress.toLowerCase() !== usdcAddress.toLowerCase()) {
      console.warn("⚠️  USDC address mismatch!");
      console.log("   Expected:", usdcAddress);
      console.log("   Actual:", contractUsdcAddress);
    }
    
    // Check constants
    const defaultFeePercentage = await wageManagement.DEFAULT_DAILY_FEE_PERCENTAGE();
    const maxFeePercentage = await wageManagement.MAX_FEE_PERCENTAGE();
    const maxBatchSize = await wageManagement.MAX_BATCH_SIZE();
    const maxHoursPerDay = await wageManagement.MAX_HOURS_PER_DAY();
    
    console.log("⚙️  Contract Constants:");
    console.log("   Default Daily Fee:", (Number(defaultFeePercentage) / 100).toFixed(2) + "%");
    console.log("   Max Fee Percentage:", (Number(maxFeePercentage) / 100).toFixed(2) + "%");
    console.log("   Max Batch Size:", maxBatchSize.toString());
    console.log("   Max Hours Per Day:", ethers.formatEther(maxHoursPerDay));
    
    // Check current state
    const companyCounter = await wageManagement.companyCounter();
    const currentCompanyId = await wageManagement.getCurrentCompanyId();
    const dailyFeePercentage = await wageManagement.dailyFeePercentage();
    
    console.log("📊 Current State:");
    console.log("   Company Counter:", companyCounter.toString());
    console.log("   Current Company ID:", currentCompanyId.toString());
    console.log("   Daily Fee Percentage:", (Number(dailyFeePercentage) / 100).toFixed(2) + "%");
    
    // Check if contract is paused
    const isPaused = await wageManagement.paused();
    console.log("⏸️  Contract Paused:", isPaused);
    
    // Get deployer/super admin info
    const [deployer] = await ethers.getSigners();
    const isSuperAdmin = await wageManagement.isSuperAdmin(deployer.address);
    const hasDefaultAdminRole = await wageManagement.hasRole(await wageManagement.DEFAULT_ADMIN_ROLE(), deployer.address);
    
    console.log("👤 Deployer Information:");
    console.log("   Address:", deployer.address);
    console.log("   Is Super Admin:", isSuperAdmin);
    console.log("   Has Default Admin Role:", hasDefaultAdminRole);
    
    // Test USDC contract if it's a MockERC20
    if (usdcAddress || contractUsdcAddress) {
      try {
        const ERC20 = await ethers.getContractFactory("MockERC20");
        const usdc = ERC20.attach(contractUsdcAddress) as any;
        
        const name = await usdc.name();
        const symbol = await usdc.symbol();
        const decimals = await usdc.decimals();
        const totalSupply = await usdc.totalSupply();
        const deployerBalance = await usdc.balanceOf(deployer.address);
        
        console.log("🪙 USDC Token Information:");
        console.log("   Name:", name);
        console.log("   Symbol:", symbol);
        console.log("   Decimals:", decimals);
        console.log("   Total Supply:", ethers.formatUnits(totalSupply, decimals));
        console.log("   Deployer Balance:", ethers.formatUnits(deployerBalance, decimals));
        
        // Check if it's a MockERC20 by trying to call mint function
        try {
          const mintFunction = usdc.interface.getFunction("mint");
          if (mintFunction) {
            console.log("🧪 This is a MockERC20 (has mint function)");
          }
        } catch {
          console.log("🏭 This is a production USDC contract");
        }
        
      } catch (error) {
        console.log("⚠️  Could not verify USDC contract details");
      }
    }
    
    console.log("\n✅ Deployment verification completed successfully!");
    
    // Provide next steps
    console.log("\n📝 Next Steps:");
    console.log("1. Add companies: wageManagement.addCompany(name, adminAddress)");
    console.log("2. Set wage rates: wageManagement.setWageRate(companyId, wageType, rate)");
    console.log("3. Deposit funds: wageManagement.depositFunds(companyId, amount)");
    console.log("4. Process payments: wageManagement.payWage(...)");
    
    if (!isSuperAdmin) {
      console.log("\n⚠️  Warning: Current account is not a super admin!");
      console.log("   You may need to use a different account for admin operations.");
    }
    
  } catch (error) {
    console.error("❌ Verification failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });