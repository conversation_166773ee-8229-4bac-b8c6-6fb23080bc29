// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title WageManagement
 * <AUTHOR>
 * @notice A secure smart contract for managing company wage payments using USDC
 * @dev This contract handles multi-company wage management with role-based access control,
 *      fee management, and batch payment capabilities
 */
contract WageManagement is ReentrancyGuard, AccessControl, Pausable {
    using SafeERC20 for IERC20;
    
    /* ========== STATE VARIABLES ========== */
    
    /// @notice The USDC token contract used for all payments
    IERC20 public immutable usdc;
    
    /// @notice Role identifier for super administrators
    bytes32 public constant SUPER_ADMIN_ROLE = keccak256("SUPER_ADMIN_ROLE");
    
    /// @notice Default daily fee percentage (5.00% scaled by 100)
    uint256 public constant DEFAULT_DAILY_FEE_PERCENTAGE = 500;
    
    /// @notice Divisor for percentage calculations (allows 2 decimal precision)
    uint256 public constant PERCENTAGE_DIVISOR = 10000;
    
    /// @notice Maximum allowable fee percentage (20.00%)
    uint256 public constant MAX_FEE_PERCENTAGE = 2000;
    
    /// @notice Maximum number of payments allowed in a single batch
    uint256 public constant MAX_BATCH_SIZE = 50;
    
    /// @notice Maximum hours allowed per single payment (24 hours in 1e18 scale)
    uint256 public constant MAX_HOURS_PER_DAY = 24 * 1e18;
    
    /// @notice Counter for generating unique company IDs
    uint256 public companyCounter = 1;
    
    /// @notice Global daily fee percentage for all companies
    uint256 public dailyFeePercentage = DEFAULT_DAILY_FEE_PERCENTAGE;
    
    /* ========== STRUCTS ========== */
    
    /**
     * @notice Structure representing a company in the system
     * @param id Unique identifier for the company
     * @param name Company name (must be unique)
     * @param admins Array of admin addresses for this company
     * @param isAdmin Mapping to check if an address is an admin
     * @param totalDeposited Total amount ever deposited by the company
     * @param currentBalance Current available balance for wage payments
     * @param feeWallet Address where fees are sent (if set)
     * @param wageRates Mapping of wage type to hourly rate (scaled by 100)
     * @param wageTypes Array of available wage types for this company
     * @param dailyFeePercentage Company-specific daily fee percentage
     * @param exists Whether this company exists in the system
     * @param isActive Whether this company is currently active
     */
    struct Company {
        uint256 id;
        string name;
        address[] admins;
        mapping(address => bool) isAdmin;
        uint256 totalDeposited;
        uint256 currentBalance;
        address feeWallet;
        mapping(string => uint256) wageRates;
        string[] wageTypes;
        uint256 dailyFeePercentage;
        bool exists;
        bool isActive;
    }
    
    /**
     * @notice Structure representing a single payment request in batch operations
     * @param laborer Address of the worker receiving payment
     * @param companyId ID of the company making the payment
     * @param hoursWorked Number of hours worked (scaled by 1e18 for decimals)
     * @param wageType Type of wage (must exist in company's wage rates)
     * @param isDailyPayment Whether this is a daily payment (affects fee calculation)
     */
    struct PaymentRequest {
        address laborer;
        uint256 companyId;
        uint256 hoursWorked;
        string wageType;
        bool isDailyPayment;
    }
    
    /* ========== MAPPINGS ========== */
    
    /// @notice Mapping from company ID to Company struct
    mapping(uint256 => Company) public companies;
    
    /// @notice Mapping from admin address to their company ID (0 if not admin)
    mapping(address => uint256) public adminToCompany;
    
    /// @notice Mapping to track if a company name is already taken
    mapping(string => bool) public companyNameExists;
    
    /// @notice Mapping to track last payment time for rate limiting (laborer => companyId => timestamp)
    mapping(address => mapping(uint256 => uint256)) public lastPaymentTime;
    
    /* ========== EVENTS ========== */
    
    /// @notice Emitted when a new super admin is added
    event SuperAdminAdded(address indexed admin);
    
    /// @notice Emitted when a super admin is removed
    event SuperAdminRemoved(address indexed admin);
    
    /// @notice Emitted when a new company is added to the system
    event CompanyAdded(uint256 indexed companyId, string name, address indexed admin);
    
    /// @notice Emitted when a new admin is added to a company
    event CompanyAdminAdded(uint256 indexed companyId, address indexed admin);
    
    /// @notice Emitted when an admin is removed from a company
    event CompanyAdminRemoved(uint256 indexed companyId, address indexed admin);
    
    /// @notice Emitted when a company is deactivated
    event CompanyDeactivated(uint256 indexed companyId);
    
    /// @notice Emitted when a company is reactivated
    event CompanyReactivated(uint256 indexed companyId);
    
    /// @notice Emitted when a wage rate is set for a company
    event WageRateSet(uint256 indexed companyId, string wageType, uint256 rate);
    
    /// @notice Emitted when funds are deposited to a company
    event FundsDeposited(uint256 indexed companyId, uint256 amount, uint256 newBalance);
    
    /// @notice Emitted when a wage payment is made
    event WagePaid(
        uint256 indexed companyId,
        address indexed laborer,
        uint256 amount,
        uint256 hoursWorked,
        string wageType,
        bool isDailyPayment,
        uint256 feeAmount
    );
    
    /// @notice Emitted when a batch of wage payments is completed
    event BatchWagePaid(uint256 indexed companyId, uint256 totalAmount, uint256 totalFee, uint256 laborersCount);
    
    /// @notice Emitted when a company's fee wallet is set
    event FeeWalletSet(uint256 indexed companyId, address indexed feeWallet);
    
    /// @notice Emitted when a company's fee percentage is changed
    event CompanyFeePercentageChanged(uint256 indexed companyId, uint256 oldPercentage, uint256 newPercentage);
    
    /// @notice Emitted when the global daily fee percentage is changed
    event DailyFeePercentageChanged(uint256 oldPercentage, uint256 newPercentage);
    
    /// @notice Emitted when emergency withdrawal is performed
    event EmergencyWithdraw(uint256 indexed companyId, address indexed admin, uint256 amount);
    
    /* ========== ERRORS ========== */
    
    /// @notice Thrown when an invalid address (zero address) is provided
    error InvalidAddress();
    
    /// @notice Thrown when trying to access a non-existent company
    error CompanyNotFound();
    
    /// @notice Thrown when trying to perform operations on an inactive company
    error CompanyInactive();
    
    /// @notice Thrown when trying to assign an admin who is already assigned
    error AdminAlreadyAssigned();
    
    /// @notice Thrown when a non-admin tries to perform admin-only operations
    error NotCompanyAdmin();
    
    /// @notice Thrown when an admin tries to remove themselves
    error CannotRemoveSelf();
    
    /// @notice Thrown when there are insufficient funds for an operation
    error InsufficientBalance();
    
    /// @notice Thrown when an invalid amount is provided
    error InvalidAmount();
    
    /// @notice Thrown when an invalid wage type is provided
    error InvalidWageType();
    
    /// @notice Thrown when a wage type is not found in company's rates
    error WageTypeNotFound();
    
    /// @notice Thrown when too many payments are submitted in a batch
    error TooManyPayments();
    
    /// @notice Thrown when batch payments contain mismatched company IDs
    error CompanyMismatch();
    
    /// @notice Thrown when invalid hours are provided
    error InvalidHours();
    
    /// @notice Thrown when trying to create a company with an existing name
    error CompanyNameAlreadyExists();
    
    /// @notice Thrown when an empty company name is provided
    error EmptyCompanyName();
    
    /// @notice Thrown when an invalid fee percentage is provided
    error InvalidFeePercentage();
    
    /// @notice Thrown when payments are made too frequently (rate limiting)
    error PaymentTooFrequent();
    
    /// @notice Thrown when excessive hours are reported in a single payment
    error ExcessiveHours();
    
    /// @notice Thrown when trying to withdraw from zero balance
    error ZeroBalance();
    
    /// @notice Thrown when a token transfer fails
    error TransferFailed();
    
    /* ========== MODIFIERS ========== */
    
    /**
     * @notice Restricts access to super administrators only
     */
    modifier onlySuperAdmin() {
        _checkRole(SUPER_ADMIN_ROLE);
        _;
    }
    
    /**
     * @notice Restricts access to admins of a specific active company
     * @param companyId The ID of the company to check admin status for
     */
    modifier onlyCompanyAdmin(uint256 companyId) {
        if (!companies[companyId].exists) revert CompanyNotFound();
        if (!companies[companyId].isActive) revert CompanyInactive();
        if (!companies[companyId].isAdmin[msg.sender]) revert NotCompanyAdmin();
        _;
    }
    
    /**
     * @notice Ensures the company exists and is active
     * @param companyId The ID of the company to validate
     */
    modifier validActiveCompany(uint256 companyId) {
        if (!companies[companyId].exists) revert CompanyNotFound();
        if (!companies[companyId].isActive) revert CompanyInactive();
        _;
    }
    
    /**
     * @notice Ensures the company exists (regardless of active status)
     * @param companyId The ID of the company to validate
     */
    modifier validCompany(uint256 companyId) {
        if (!companies[companyId].exists) revert CompanyNotFound();
        _;
    }
    
    /* ========== CONSTRUCTOR ========== */
    
    /**
     * @notice Initializes the WageManagement contract
     * @param _usdcAddress Address of the USDC token contract
     * @dev Sets up the initial super admin role for the deployer
     */
    constructor(address _usdcAddress) {
        if (_usdcAddress == address(0)) revert InvalidAddress();
        usdc = IERC20(_usdcAddress);
        
        // Set up roles
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(SUPER_ADMIN_ROLE, msg.sender);
        
        emit SuperAdminAdded(msg.sender);
    }
    
    /* ========== SUPER ADMIN FUNCTIONS ========== */
    
    /**
     * @notice Adds a new super administrator
     * @param admin Address to grant super admin privileges
     * @dev Only existing super admins can add new ones
     */
    function addSuperAdmin(address admin) external onlySuperAdmin {
        if (admin == address(0)) revert InvalidAddress();
        if (!hasRole(SUPER_ADMIN_ROLE, admin)) {
            _grantRole(SUPER_ADMIN_ROLE, admin);
            emit SuperAdminAdded(admin);
        }
    }
    
    /**
     * @notice Removes a super administrator
     * @param admin Address to revoke super admin privileges from
     * @dev Super admins cannot remove themselves to prevent lockout
     */
    function removeSuperAdmin(address admin) external onlySuperAdmin {
        if (admin == msg.sender) revert CannotRemoveSelf();
        if (hasRole(SUPER_ADMIN_ROLE, admin)) {
            _revokeRole(SUPER_ADMIN_ROLE, admin);
            emit SuperAdminRemoved(admin);
        }
    }
    
    /**
     * @notice Creates a new company in the system
     * @param name Unique name for the company (max 100 characters)
     * @param admin Address that will become the first admin of this company
     * @dev Company names must be unique and non-empty
     */
    function addCompany(string calldata name, address admin) external onlySuperAdmin {
        if (bytes(name).length == 0) revert EmptyCompanyName();
        if (bytes(name).length > 100) revert InvalidAmount(); // Limit company name length
        if (admin == address(0)) revert InvalidAddress();
        if (adminToCompany[admin] != 0) revert AdminAlreadyAssigned();
        if (companyNameExists[name]) revert CompanyNameAlreadyExists();
        
        uint256 companyId = companyCounter++;
        Company storage newCompany = companies[companyId];
        newCompany.id = companyId;
        newCompany.name = name;
        newCompany.exists = true;
        newCompany.isActive = true;
        newCompany.admins.push(admin);
        newCompany.isAdmin[admin] = true;
        newCompany.dailyFeePercentage = DEFAULT_DAILY_FEE_PERCENTAGE;
        
        adminToCompany[admin] = companyId;
        companyNameExists[name] = true;
        
        emit CompanyAdded(companyId, name, admin);
    }
    
    /**
     * @notice Deactivates a company, preventing all operations
     * @param companyId ID of the company to deactivate
     * @dev Deactivated companies cannot perform any wage operations but funds remain accessible
     */
    function deactivateCompany(uint256 companyId) external onlySuperAdmin validCompany(companyId) {
        companies[companyId].isActive = false;
        emit CompanyDeactivated(companyId);
    }
    
    /**
     * @notice Reactivates a previously deactivated company
     * @param companyId ID of the company to reactivate
     */
    function reactivateCompany(uint256 companyId) external onlySuperAdmin validCompany(companyId) {
        companies[companyId].isActive = true;
        emit CompanyReactivated(companyId);
    }
    
    /**
     * @notice Changes the global daily fee percentage
     * @param newPercentage New fee percentage (scaled by 100, max 2000 = 20%)
     * @dev This affects all companies that haven't set their own fee percentage
     */
    function changeDailyFeePercentage(uint256 newPercentage) external onlySuperAdmin {
        if (newPercentage > MAX_FEE_PERCENTAGE) revert InvalidFeePercentage();
        uint256 oldPercentage = dailyFeePercentage;
        dailyFeePercentage = newPercentage;
        emit DailyFeePercentageChanged(oldPercentage, newPercentage);
    }
    
    /**
     * @notice Pauses all contract operations in case of emergency
     * @dev Only super admins can pause the contract
     */
    function pause() external onlySuperAdmin {
        _pause();
    }
    
    /**
     * @notice Unpauses the contract to resume normal operations
     * @dev Only super admins can unpause the contract
     */
    function unpause() external onlySuperAdmin {
        _unpause();
    }
    
    /* ========== COMPANY ADMIN FUNCTIONS ========== */
    
    /**
     * @notice Adds a new administrator to a company
     * @param companyId ID of the company to add admin to
     * @param admin Address of the new administrator
     * @dev New admins cannot already be admins of any other company
     */
    function addCompanyAdmin(uint256 companyId, address admin) external onlyCompanyAdmin(companyId) {
        if (admin == address(0)) revert InvalidAddress();
        if (companies[companyId].isAdmin[admin]) revert AdminAlreadyAssigned();
        if (adminToCompany[admin] != 0) revert AdminAlreadyAssigned();
        
        companies[companyId].admins.push(admin);
        companies[companyId].isAdmin[admin] = true;
        adminToCompany[admin] = companyId;
        
        emit CompanyAdminAdded(companyId, admin);
    }
    
    /**
     * @notice Removes an administrator from a company
     * @param companyId ID of the company to remove admin from
     * @param admin Address of the administrator to remove
     * @dev Admins cannot remove themselves to prevent lockout
     */
    function removeCompanyAdmin(uint256 companyId, address admin) external onlyCompanyAdmin(companyId) {
        if (admin == msg.sender) revert CannotRemoveSelf();
        if (!companies[companyId].isAdmin[admin]) revert NotCompanyAdmin();
        
        companies[companyId].isAdmin[admin] = false;
        adminToCompany[admin] = 0;
        
        // Remove from admins array
        address[] storage admins = companies[companyId].admins;
        for (uint256 i = 0; i < admins.length; i++) {
            if (admins[i] == admin) {
                admins[i] = admins[admins.length - 1];
                admins.pop();
                break;
            }
        }
        
        emit CompanyAdminRemoved(companyId, admin);
    }
    
    /**
     * @notice Sets the hourly wage rate for a specific wage type
     * @param companyId ID of the company setting the rate
     * @param wageType Name of the wage type (e.g., "regular", "overtime")
     * @param hourlyRate Hourly rate in USDC cents (e.g., 1500 = $15.00/hour)
     * @dev Wage types are limited to 50 characters and companies can have max 50 types
     */
    function setWageRate(uint256 companyId, string calldata wageType, uint256 hourlyRate) 
        external 
        onlyCompanyAdmin(companyId) 
    {
        if (bytes(wageType).length == 0 || bytes(wageType).length > 50) revert InvalidWageType();
        if (hourlyRate == 0 || hourlyRate > 1000000 * 100) revert InvalidAmount(); // Max $10,000/hour
        
        // Add to wage types array if it's a new type
        if (companies[companyId].wageRates[wageType] == 0) {
            if (companies[companyId].wageTypes.length >= 50) revert InvalidAmount(); // Limit wage types
            companies[companyId].wageTypes.push(wageType);
        }
        
        companies[companyId].wageRates[wageType] = hourlyRate;
        emit WageRateSet(companyId, wageType, hourlyRate);
    }
    
    /**
     * @notice Sets the fee wallet address where fees will be sent
     * @param companyId ID of the company setting the fee wallet
     * @param feeWallet Address where fees should be sent
     * @dev If no fee wallet is set, fees remain in the contract
     */
    function setFeeWallet(uint256 companyId, address feeWallet) external onlyCompanyAdmin(companyId) {
        if (feeWallet == address(0)) revert InvalidAddress();
        companies[companyId].feeWallet = feeWallet;
        emit FeeWalletSet(companyId, feeWallet);
    }
    
    /**
     * @notice Sets a company-specific daily fee percentage
     * @param companyId ID of the company setting the fee
     * @param newPercentage New fee percentage (scaled by 100, max 2000 = 20%)
     * @dev Company-specific fees override the global daily fee percentage
     */
    function setCompanyFeePercentage(uint256 companyId, uint256 newPercentage) external onlyCompanyAdmin(companyId) {
        if (newPercentage > MAX_FEE_PERCENTAGE) revert InvalidFeePercentage();
        uint256 oldPercentage = companies[companyId].dailyFeePercentage;
        companies[companyId].dailyFeePercentage = newPercentage;
        emit CompanyFeePercentageChanged(companyId, oldPercentage, newPercentage);
    }
    
    /**
     * @notice Deposits USDC funds into a company's balance
     * @param companyId ID of the company to deposit funds for
     * @param amount Amount of USDC to deposit (in token's smallest unit)
     * @dev Requires prior approval of USDC transfer to this contract
     */
    function depositFunds(uint256 companyId, uint256 amount) external onlyCompanyAdmin(companyId) whenNotPaused {
        if (amount == 0) revert InvalidAmount();
        
        usdc.safeTransferFrom(msg.sender, address(this), amount);
        
        companies[companyId].totalDeposited += amount;
        companies[companyId].currentBalance += amount;
        
        emit FundsDeposited(companyId, amount, companies[companyId].currentBalance);
    }
    
    /**
     * @notice Pays wages to a single laborer
     * @param laborer Address of the worker to pay
     * @param companyId ID of the company making the payment
     * @param hoursWorked Number of hours worked (scaled by 1e18, e.g., 1.5 hours = 1.5e18)
     * @param wageType Type of wage to use for rate calculation
     * @param isDailyPayment Whether this is a daily payment (affects fee calculation)
     * @dev Includes rate limiting to prevent payment spam (minimum 1 hour between payments)
     */
    function payWage(
        address laborer,
        uint256 companyId,
        uint256 hoursWorked,
        string calldata wageType,
        bool isDailyPayment
    ) external onlyCompanyAdmin(companyId) nonReentrant whenNotPaused {
        _processSinglePayment(laborer, companyId, hoursWorked, wageType, isDailyPayment);
    }
    
    /**
     * @notice Pays wages to multiple laborers in a single transaction
     * @param payments Array of payment requests (max 50 payments per batch)
     * @dev All payments must be for the same company. Uses checks-effects-interactions pattern
     */
    function payMultipleWages(PaymentRequest[] calldata payments) 
        external 
        nonReentrant 
        whenNotPaused
    {
        if (payments.length == 0) revert InvalidAmount();
        if (payments.length > MAX_BATCH_SIZE) revert TooManyPayments();
        
        uint256 companyId = payments[0].companyId;
        if (!companies[companyId].exists) revert CompanyNotFound();
        if (!companies[companyId].isActive) revert CompanyInactive();
        if (!companies[companyId].isAdmin[msg.sender]) revert NotCompanyAdmin();
        
        uint256 totalAmount = 0;
        uint256 totalFee = 0;
        
        // Pre-calculate total to check balance once
        for (uint256 i = 0; i < payments.length; i++) {
            if (payments[i].companyId != companyId) revert CompanyMismatch();
            
            (uint256 amount, uint256 fee) = _calculatePayment(
                companyId,
                payments[i].hoursWorked,
                payments[i].wageType,
                payments[i].isDailyPayment
            );
            
            totalAmount += amount;
            totalFee += fee;
        }
        
        // Check sufficient balance
        if (companies[companyId].currentBalance < totalAmount + totalFee) revert InsufficientBalance();
        
        // Update company balance first (CEI pattern)
        companies[companyId].currentBalance -= (totalAmount + totalFee);
        
        // Process each payment
        for (uint256 i = 0; i < payments.length; i++) {
            _validatePayment(payments[i].laborer, companyId, payments[i].hoursWorked);
            
            (uint256 amount, uint256 fee) = _calculatePayment(
                companyId,
                payments[i].hoursWorked,
                payments[i].wageType,
                payments[i].isDailyPayment
            );
            
            // Transfer to laborer
            usdc.safeTransfer(payments[i].laborer, amount);
            
            // Update last payment time
            lastPaymentTime[payments[i].laborer][companyId] = block.timestamp;
            
            emit WagePaid(
                companyId,
                payments[i].laborer,
                amount,
                payments[i].hoursWorked,
                payments[i].wageType,
                payments[i].isDailyPayment,
                fee
            );
        }
        
        // Transfer fees if applicable
        if (totalFee > 0 && companies[companyId].feeWallet != address(0)) {
            usdc.safeTransfer(companies[companyId].feeWallet, totalFee);
        }
        
        emit BatchWagePaid(companyId, totalAmount, totalFee, payments.length);
    }
    
    /* ========== INTERNAL FUNCTIONS ========== */
    
    /**
     * @notice Internal function to process a single wage payment
     * @param laborer Address of the worker to pay
     * @param companyId ID of the company making the payment
     * @param hoursWorked Number of hours worked (scaled by 1e18)
     * @param wageType Type of wage for rate calculation
     * @param isDailyPayment Whether this is a daily payment
     * @dev Handles validation, calculation, and transfers for individual payments
     */
    function _processSinglePayment(
        address laborer,
        uint256 companyId,
        uint256 hoursWorked,
        string calldata wageType,
        bool isDailyPayment
    ) internal {
        _validatePayment(laborer, companyId, hoursWorked);
        
        (uint256 amount, uint256 fee) = _calculatePayment(companyId, hoursWorked, wageType, isDailyPayment);
        
        if (companies[companyId].currentBalance < amount + fee) revert InsufficientBalance();
        
        // Update company balance
        companies[companyId].currentBalance -= (amount + fee);
        
        // Transfer wage to laborer
        usdc.safeTransfer(laborer, amount);
        
        // Transfer fee if applicable
        if (fee > 0 && companies[companyId].feeWallet != address(0)) {
            usdc.safeTransfer(companies[companyId].feeWallet, fee);
        }
        
        // Update last payment time
        lastPaymentTime[laborer][companyId] = block.timestamp;
        
        emit WagePaid(companyId, laborer, amount, hoursWorked, wageType, isDailyPayment, fee);
    }
    
    /**
     * @notice Validates payment parameters and rate limits
     * @param laborer Address of the worker (cannot be zero address)
     * @param companyId ID of the company making payment
     * @param hoursWorked Number of hours (must be > 0 and <= 24 hours)
     * @dev Implements rate limiting to prevent payment spam
     */
    function _validatePayment(address laborer, uint256 companyId, uint256 hoursWorked) internal view {
        if (laborer == address(0)) revert InvalidAddress();
        if (hoursWorked == 0) revert InvalidHours();
        if (hoursWorked > MAX_HOURS_PER_DAY) revert ExcessiveHours();
        
        // Prevent payment spam (minimum 1 hour between payments)
        if (block.timestamp - lastPaymentTime[laborer][companyId] < 3600) {
            revert PaymentTooFrequent();
        }
    }
    
    /**
     * @notice Calculates payment amount and fees
     * @param companyId ID of the company making payment
     * @param hoursWorked Number of hours worked (scaled by 1e18)
     * @param wageType Type of wage for rate lookup
     * @param isDailyPayment Whether daily fees apply
     * @return amount Net amount to pay to laborer
     * @return fee Fee amount (if applicable)
     * @dev Uses company-specific fee percentage if set, otherwise global percentage
     */
    function _calculatePayment(
        uint256 companyId,
        uint256 hoursWorked,
        string memory wageType,
        bool isDailyPayment
    ) internal view returns (uint256 amount, uint256 fee) {
        uint256 hourlyRate = companies[companyId].wageRates[wageType];
        if (hourlyRate == 0) revert WageTypeNotFound();
        
        // Calculate base amount (hoursWorked is scaled by 1e18)
        amount = (hoursWorked * hourlyRate) / 1e18;
        
        // Calculate fee if daily payment
        if (isDailyPayment) {
            uint256 feePercentage = companies[companyId].dailyFeePercentage > 0 
                ? companies[companyId].dailyFeePercentage 
                : dailyFeePercentage;
            fee = (amount * feePercentage) / PERCENTAGE_DIVISOR;
        } else {
            fee = 0;
        }
    }
    
    /* ========== VIEW FUNCTIONS ========== */
    
    /**
     * @notice Gets comprehensive information about a company
     * @param companyId ID of the company to query
     * @return name Company name
     * @return admins Array of admin addresses
     * @return totalDeposited Total amount ever deposited
     * @return currentBalance Current available balance
     * @return feeWallet Address where fees are sent
     * @return wageTypes Array of available wage types
     * @return isActive Whether the company is currently active
     */
    function getCompanyInfo(uint256 companyId) 
        external 
        view 
        validCompany(companyId)
        returns (
            string memory name,
            address[] memory admins,
            uint256 totalDeposited,
            uint256 currentBalance,
            address feeWallet,
            string[] memory wageTypes,
            bool isActive
        ) 
    {
        Company storage company = companies[companyId];
        return (
            company.name,
            company.admins,
            company.totalDeposited,
            company.currentBalance,
            company.feeWallet,
            company.wageTypes,
            company.isActive
        );
    }
    
       /**
     * @notice Returns the wage rate for a given company and wage type.
     * @param companyId The ID of the company.
     * @param wageType The wage type (e.g., "hourly", "daily").
     * @return Wage rate associated with the wage type for the specified company.
     */
    function getWageRate(uint256 companyId, string calldata wageType) 
        external 
        view 
        validCompany(companyId)
        returns (uint256) 
    {
        return companies[companyId].wageRates[wageType];
    }

    /**
     * @notice Checks if a given address is an admin of the specified company.
     * @param companyId The ID of the company.
     * @param admin Address to check for admin rights.
     * @return True if the address is an admin, false otherwise.
     */
    function isCompanyAdmin(uint256 companyId, address admin) 
        external 
        view 
        validCompany(companyId)
        returns (bool) 
    {
        return companies[companyId].isAdmin[admin];
    }

    /**
     * @notice Determines whether an address has super admin privileges.
     * @param account Address to check.
     * @return True if the address is a super admin, false otherwise.
     */
    function isSuperAdmin(address account) external view returns (bool) {
        return hasRole(SUPER_ADMIN_ROLE, account);
    }

    /**
     * @notice Calculates the wage amount and fee for a laborer based on company rates.
     * @param companyId The ID of the company.
     * @param hoursWorked Number of hours the laborer worked.
     * @param wageType The wage type (e.g., "hourly", "daily").
     * @param isDailyPayment Whether the payment is calculated daily.
     * @return amount Base wage amount.
     * @return fee Fee charged on the wage.
     * @return total Total payment amount including fee.
     */
    function calculateWageAmount(
        uint256 companyId,
        uint256 hoursWorked,
        string calldata wageType,
        bool isDailyPayment
    ) external view validActiveCompany(companyId) returns (uint256 amount, uint256 fee, uint256 total) {
        (amount, fee) = _calculatePayment(companyId, hoursWorked, wageType, isDailyPayment);
        total = amount + fee;
    }

    /**
     * @notice Gets the ID of the most recently created company.
     * @return The latest company ID.
     */
    function getCurrentCompanyId() external view returns (uint256) {
        return companyCounter - 1;
    }

    /**
     * @notice Retrieves the list of admin addresses for a given company.
     * @param companyId The ID of the company.
     * @return Array of admin addresses.
     */
    function getAllCompanyAdmins(uint256 companyId) 
        external 
        view 
        validCompany(companyId) 
        returns (address[] memory) 
    {
        return companies[companyId].admins;
    }

    /**
     * @notice Retrieves all wage types and their corresponding rates for a company.
     * @param companyId The ID of the company.
     * @return wageTypes Array of wage type strings.
     * @return rates Array of wage rate values.
     */
    function getCompanyWageRates(uint256 companyId) 
        external 
        view 
        validCompany(companyId) 
        returns (string[] memory wageTypes, uint256[] memory rates) 
    {
        string[] memory types = companies[companyId].wageTypes;
        uint256[] memory wageRates = new uint256[](types.length);
        
        for (uint256 i = 0; i < types.length; i++) {
            wageRates[i] = companies[companyId].wageRates[types[i]];
        }
        
        return (types, wageRates);
    }

    /**
     * @notice Checks if a company name has already been taken.
     * @param name The name of the company.
     * @return True if the name is taken, false otherwise.
     */
    function isCompanyNameTaken(string calldata name) external view returns (bool) {
        return companyNameExists[name];
    }

    /**
     * @notice Returns the last time a laborer was paid by a specific company.
     * @param laborer The laborer's wallet address.
     * @param companyId The ID of the company.
     * @return Timestamp of the last payment.
     */
    function getLastPaymentTime(address laborer, uint256 companyId) external view returns (uint256) {
        return lastPaymentTime[laborer][companyId];
    }

    /**
     * @notice Allows a company admin to withdraw funds in an emergency.
     * @dev Reverts if amount is zero or more than company balance.
     * @param companyId The ID of the company.
     * @param amount The amount to withdraw.
     */
    function emergencyWithdraw(uint256 companyId, uint256 amount) 
        external 
        onlyCompanyAdmin(companyId) 
        nonReentrant
    {
        if (amount == 0) revert InvalidAmount();
        if (amount > companies[companyId].currentBalance) revert InsufficientBalance();
        
        companies[companyId].currentBalance -= amount;
        usdc.safeTransfer(msg.sender, amount);
        
        emit EmergencyWithdraw(companyId, msg.sender, amount);
    }

    /**
     * @notice Checks if the contract supports a specific interface (for AccessControl or Pausable).
     * @param interfaceId The ID of the interface.
     * @return True if the interface is supported, false otherwise.
     */
    function supportsInterface(bytes4 interfaceId) 
        public 
        view 
        virtual 
        override(AccessControl) 
        returns (bool) 
    {
        return super.supportsInterface(interfaceId);
    }
}