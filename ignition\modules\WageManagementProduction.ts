import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";

const WageManagementProductionModule = buildModule("WageManagementProductionModule", (m) => {
  // For production deployment, use existing USDC contract address
  // Default to Ethereum mainnet USDC address, can be overridden via parameters
  const usdcAddress = m.getParameter("usdcAddress", "******************************************"); // Ethereum mainnet USDC
  
  // Deploy WageManagement contract with existing USDC address
  const wageManagement = m.contract("WageManagement", [usdcAddress]);

  return {
    wageManagement
  };
});

export default WageManagementProductionModule;