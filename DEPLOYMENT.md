# WageManagement Deployment Guide

This guide provides step-by-step instructions for deploying the WageManagement smart contract system.

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Yarn** or **npm**
3. **Hardhat** development environment
4. **Wallet** with sufficient funds for deployment
5. **RPC endpoints** for target networks

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd day-pay-smart-contract

# Install dependencies
npm install
# or
yarn install

# Compile contracts
npm run compile
```

## Environment Setup

Create a `.env` file in the root directory:

```env
# Private key for deployment (without 0x prefix)
PRIVATE_KEY=your_private_key_here

# RPC URLs
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_project_id
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/your_project_id
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your_project_id
ARBITRUM_RPC_URL=https://arbitrum-mainnet.infura.io/v3/your_project_id

# Etherscan API keys for verification
ETHERSCAN_API_KEY=your_etherscan_api_key
POLYGONSCAN_API_KEY=your_polygonscan_api_key
ARBISCAN_API_KEY=your_arbiscan_api_key

# Optional: USDC contract address for production deployments
USDC_ADDRESS=******************************************
```

## Deployment Methods

### Method 1: Using Custom Deploy Script (Recommended)

#### Local Development
```bash
# Start local Hardhat node
npm run node

# In another terminal, deploy to local network
npm run deploy:local
```

#### Testnet Deployment
```bash
# Deploy to Sepolia testnet
npm run deploy:sepolia
```

#### Mainnet Deployment
```bash
# Deploy to Ethereum mainnet
npm run deploy:mainnet

# Deploy to Polygon
npm run deploy:polygon

# Deploy to Arbitrum
npm run deploy:arbitrum
```

### Method 2: Using Hardhat Ignition

#### Development (with MockERC20)
```bash
# Deploy to local network
npm run ignition:local

# Deploy to Sepolia testnet
npm run ignition:sepolia
```

#### Production (with existing USDC)
```bash
# Deploy to mainnet
npm run ignition:mainnet

# Deploy with custom USDC address
npx hardhat ignition deploy ./ignition/modules/WageManagementProduction.ts --network mainnet --parameters '{"WageManagementProductionModule": {"usdcAddress": "0xYourUSDCAddress"}}'
```

## Network-Specific Deployments

### Ethereum Mainnet
```bash
# Using deploy script
npm run deploy:mainnet

# Using Ignition
npx hardhat ignition deploy ./ignition/modules/WageManagementProduction.ts --network mainnet
```

### Polygon
```bash
npm run deploy:polygon
```

### Arbitrum
```bash
npm run deploy:arbitrum
```

### Sepolia Testnet
```bash
npm run deploy:sepolia
```

## Post-Deployment Verification

### Verify Deployment
```bash
# Verify deployment with contract address
npm run verify -- <WAGE_MANAGEMENT_ADDRESS> [USDC_ADDRESS]

# Example
npm run verify -- ****************************************** ******************************************
```

### Contract Verification on Etherscan
```bash
# Verify on Etherscan
npx hardhat verify --network mainnet <WAGE_MANAGEMENT_ADDRESS> "<USDC_ADDRESS>"

# Verify on Polygonscan
npx hardhat verify --network polygon <WAGE_MANAGEMENT_ADDRESS> "<USDC_ADDRESS>"
```

## Example Usage

Run the example script to see the contract in action:

```bash
# Start local node
npm run node

# Deploy contracts
npm run deploy:local

# Run example usage
npm run example
```

## Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Sufficient funds in deployment wallet
- [ ] Contracts compiled successfully
- [ ] Tests passing
- [ ] Network configuration verified

### During Deployment
- [ ] Monitor gas prices
- [ ] Confirm transaction details
- [ ] Save deployment addresses
- [ ] Verify contract deployment

### Post-Deployment
- [ ] Verify contracts on block explorer
- [ ] Test basic functionality
- [ ] Set up initial configuration
- [ ] Document deployment details
- [ ] Update frontend configuration

## Initial Configuration

After deployment, perform these initial setup steps:

### 1. Add Super Admins (if needed)
```solidity
wageManagement.addSuperAdmin(adminAddress);
```

### 2. Add First Company
```solidity
wageManagement.addCompany("Company Name", companyAdminAddress);
```

### 3. Configure Company Settings
```solidity
// Set wage rates
wageManagement.setWageRate(companyId, "regular", 1500); // $15.00/hour
wageManagement.setWageRate(companyId, "overtime", 2250); // $22.50/hour

// Set fee wallet
wageManagement.setFeeWallet(companyId, feeWalletAddress);

// Deposit initial funds
usdc.approve(wageManagementAddress, amount);
wageManagement.depositFunds(companyId, amount);
```

## Troubleshooting

### Common Issues

#### 1. Insufficient Gas
- Increase gas limit in hardhat.config.ts
- Check current gas prices on the network

#### 2. USDC Address Issues
- Verify USDC contract address for the target network
- Ensure USDC contract is deployed on the network

#### 3. Permission Errors
- Verify deployer has sufficient permissions
- Check if wallet has enough funds

#### 4. Network Configuration
- Verify RPC URL is correct
- Check network ID matches configuration

### Gas Optimization Tips

1. **Batch Operations**: Use batch payment functions when possible
2. **Gas Price**: Monitor and set appropriate gas prices
3. **Contract Size**: Contracts are optimized but verify size limits
4. **Transaction Timing**: Deploy during low network congestion

## Security Considerations

### Pre-Deployment Security
- [ ] Code audited (recommended for mainnet)
- [ ] Private keys secured
- [ ] Multi-sig setup for admin functions
- [ ] Emergency procedures documented

### Post-Deployment Security
- [ ] Admin keys secured
- [ ] Emergency pause procedures tested
- [ ] Fee wallet security verified
- [ ] Regular security monitoring

## Monitoring and Maintenance

### Recommended Monitoring
- Contract balance levels
- Failed transactions
- Gas usage patterns
- Admin activity logs

### Regular Maintenance
- Update fee percentages as needed
- Monitor company balances
- Review admin permissions
- Update documentation

## Support

For deployment issues:
1. Check the troubleshooting section
2. Review Hardhat documentation
3. Open an issue in the repository
4. Contact the development team

## Deployment Costs

Estimated gas costs (may vary based on network conditions):

| Network | WageManagement | MockERC20 | Total (USD)* |
|---------|----------------|-----------|--------------|
| Ethereum | ~2.5M gas | ~1.2M gas | $50-200 |
| Polygon | ~2.5M gas | ~1.2M gas | $1-5 |
| Arbitrum | ~2.5M gas | ~1.2M gas | $5-20 |

*Costs are estimates and depend on current gas prices