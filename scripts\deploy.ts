import { ethers, ignition } from "hardhat";
import { getNetworkConfig } from "./deploy-config";
import WageManagementModule from "../ignition/modules/WageManagement";
import WageManagementProductionModule from "../ignition/modules/WageManagementProduction";

async function main() {
  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  const networkName = network.name === "unknown" ? "localhost" : network.name;
  
  console.log("🚀 Starting WageManagement deployment...");
  console.log("📍 Network:", networkName);
  console.log("👤 Deployer:", deployer.address);
  console.log("💰 Deployer balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)), "ETH");
  
  try {
    const networkConfig = getNetworkConfig(networkName);
    console.log("🌐 Network config:", networkConfig.name);
    
    let deploymentResult;
    
    if (networkConfig.usdc === null) {
      // Development deployment - deploy MockERC20 and WageManagement
      console.log("🧪 Development deployment - deploying MockERC20 and WageManagement...");
      
      deploymentResult = await ignition.deploy(WageManagementModule, {
        parameters: {
          WageManagementModule: {
            usdcName: "USD Coin (Test)",
            usdcSymbol: "USDC",
            usdcInitialSupply: ethers.parseUnits("1000000", 6) // 1M USDC with 6 decimals
          }
        }
      });
      
      console.log("✅ MockERC20 (USDC) deployed to:", await deploymentResult.usdc.getAddress());
      console.log("✅ WageManagement deployed to:", await deploymentResult.wageManagement.getAddress());
      
      // Mint some USDC to deployer for testing
      const usdc = deploymentResult.usdc;
      const mintTx = await usdc.mint(deployer.address, ethers.parseUnits("100000", 6)); // 100k USDC
      await mintTx.wait();
      console.log("💰 Minted 100,000 USDC to deployer for testing");
      
    } else {
      // Production deployment - use existing USDC
      console.log("🏭 Production deployment - using existing USDC at:", networkConfig.usdc);
      
      deploymentResult = await ignition.deploy(WageManagementProductionModule, {
        parameters: {
          WageManagementProductionModule: {
            usdcAddress: networkConfig.usdc
          }
        }
      });
      
      console.log("✅ WageManagement deployed to:", await deploymentResult.wageManagement.getAddress());
      console.log("🔗 Using USDC at:", networkConfig.usdc);
    }
    
    // Verify deployment
    const wageManagement = deploymentResult.wageManagement;
    const usdcAddress = await wageManagement.usdc();
    console.log("🔍 Verification - USDC address in contract:", usdcAddress);
    
    // Check if deployer is super admin
    const isSuperAdmin = await wageManagement.isSuperAdmin(deployer.address);
    console.log("👑 Deployer is super admin:", isSuperAdmin);
    
    // Get current company counter
    const currentCompanyId = await wageManagement.getCurrentCompanyId();
    console.log("🏢 Current company counter:", currentCompanyId.toString());
    
    console.log("\n🎉 Deployment completed successfully!");
    console.log("📋 Summary:");
    console.log("   - Network:", networkConfig.name);
    console.log("   - WageManagement:", await wageManagement.getAddress());
    console.log("   - USDC:", usdcAddress);
    console.log("   - Deployer (Super Admin):", deployer.address);
    
    if (networkConfig.usdc === null) {
      console.log("\n🧪 Development Setup:");
      console.log("   - MockERC20 deployed with 1M initial supply");
      console.log("   - 100k USDC minted to deployer for testing");
      console.log("   - Use the MockERC20 address for testing purposes");
    }
    
    console.log("\n📝 Next steps:");
    console.log("   1. Add companies using addCompany()");
    console.log("   2. Set wage rates for companies");
    console.log("   3. Deposit funds for wage payments");
    console.log("   4. Start processing wage payments");
    
  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });